import cv2
import numpy as np
import os

# --- 配置参数 ---
image_path = "4.jpg"             # 输入图片路径 (请确保图片和脚本在同一文件夹)
output_processed_image = "processed_outline.png" # 边缘检测后的图片预览
output_c_array_file = "image_data_for_stm32.h" # 生成的C语言头文件，包含数组

target_width_mm = 250                  # 目标物理宽度 (毫米)
target_height_mm = 250                 # 目标物理高度 (毫米)

# 步进电机校准参数 (非常重要！请根据您的云台和电机实际校准结果填写)
steps_per_mm_X = 100 / 10             # 示例: X轴电机移动50mm需要200步，所以4步/mm
steps_per_mm_Y = 100 / 10             # 示例: Y轴电机移动50mm需要200步，所以4步/mm

canny_low_threshold = 50              # Canny 边缘检测低阈值
canny_high_threshold = 300            # Canny 边缘检测高阈值

# 激光烧灼时间 (毫秒) - 当激光开启并到位后，停留的时间。
# 对于直线绘制，这个值通常设置为0，因为激光是连续开启的。
# 只有在画点时才需要设置一个非零值。
laser_burn_duration_ms = 0 # 0毫秒，意味着连续绘制，不额外停留烧灼

# 数组名称 (将用于C语言头文件)
points_array_name = "drawing_points"
metadata_array_name = "path_metadata"

# --- 点数减少设置 (为了更高精度，以下设置默认会增加点数) ---
# 方法一：降低原始图片分辨率
scale_percent = 50 # 原始尺寸100%。若要减少点数，可设为70、50等。

# 方法二：Douglas-Peucker 算法简化 (默认禁用，以保证高精度)
# 如果需要减少点数，可以取消注释并设置一个大于0的值 (例如 0.005)
# epsilon_percent = 0.005 # 值越大，点数越少，精度越低。设置为0或注释掉则不简化。

# -----------------------------------------------------------------------------
# --- 1. 图像加载与预处理 ---
print(f"正在加载图片: {image_path}")
img = cv2.imread(image_path)

if img is None:
    print(f"错误：无法加载图片 '{image_path}'。请检查路径或文件名。")
    print("确保图片文件在脚本相同的目录下，或者 'image_path' 是图片的完整路径。")
    exit()

# 降低图片分辨率
if scale_percent < 100:
    width = int(img.shape[1] * scale_percent / 100)
    height = int(img.shape[0] * scale_percent / 100)
    dim = (width, height)
    img = cv2.resize(img, dim, interpolation = cv2.INTER_AREA)
    print(f"图片已缩放到：{img.shape[1]}x{img.shape[0]}像素。")

# 转换为灰度图
gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
print("图片已转换为灰度图。")

# (可选) 高斯模糊预处理，平滑图像，减少噪点
# blurred_img = cv2.GaussianBlur(gray_img, (5, 5), 0) # 5x5是模糊核大小，可以调整
# img_for_canny = blurred_img
img_for_canny = gray_img # 默认不模糊

# --- 2. 边缘检测 ---
print(f"正在执行 Canny 边缘检测 (阈值: {canny_low_threshold}, {canny_high_threshold})...")
edges = cv2.Canny(img_for_canny, canny_low_threshold, canny_high_threshold)

# 保存处理后的边缘图片，方便预览效果
cv2.imwrite(output_processed_image, edges)
print(f"处理后的边缘图片已保存为 '{output_processed_image}'。请检查效果。")

# --- 3. 提取边缘轮廓 ---
# *** 核心修改在这里：从 cv2.RETR_EXTERNAL 变为 cv2.RETR_LIST ***
# cv2.RETR_LIST 检索所有轮廓，不建立任何等级关系
# cv2.CHAIN_APPROX_NONE 存储所有轮廓点，提供最高精度 (与 cv2.CHAIN_APPROX_SIMPLE 相对)
contours, hierarchy = cv2.findContours(edges, cv2.RETR_LIST, cv2.CHAIN_APPROX_NONE)

if not contours:
    print("警告：未检测到任何轮廓。请尝试调整 Canny 阈值或更换图片。")
    exit()

print(f"共找到 {len(contours)} 个原始轮廓。")

# --- 4. 图像坐标到物理毫米及电机脉冲的映射 ---
img_height_pixels, img_width_pixels = edges.shape # 获取图片的高度和宽度 (行, 列)

scale_factor = min(target_width_mm / img_width_pixels, target_height_mm / img_height_pixels)
actual_draw_width_mm = img_width_pixels * scale_factor
actual_draw_height_mm = img_height_pixels * scale_factor

print(f"实际绘制尺寸为: 宽度 {actual_draw_width_mm:.2f} mm, 高度 {actual_draw_height_mm:.2f} mm")

# 计算图片中心点在像素坐标系中的偏移
offset_x_pixels = img_width_pixels / 2
offset_y_pixels = img_height_pixels / 2

# 存储每个轮廓的起点 (用于后续排序)
contour_start_points_mapped = [] # 存储轮廓第一个点映射后的电机脉冲坐标 (X, Y)
processed_contours_data = [] # 存储处理后的轮廓点数据 [[(X, Y), (X, Y), ...], ...]

for i, contour in enumerate(contours):
    if len(contour) < 2: # 忽略只有1个点的轮廓
        continue

    # (可选) Douglas-Peucker 算法简化轮廓
    # 启用该功能，请在配置参数部分设置 epsilon_percent
    # 值越大，点数越少，但形状失真越大。
    # 如果要使用，请取消注释下面三行：
    if 'epsilon_percent' in locals() and 'epsilon_percent' in globals() and epsilon_percent > 0:
        epsilon = epsilon_percent * cv2.arcLength(contour, True) # 周长
        contour = cv2.approxPolyDP(contour, epsilon, True)
        if len(contour) < 2: # 简化后可能点数少于2，忽略
            continue

    current_contour_points = []
    
    for j, point in enumerate(contour):
        px, py = point[0] # contour的点是 [[[x,y]]] 这种三层列表
        
        physical_x_mm = (px - offset_x_pixels) * scale_factor
        physical_y_mm = (py - offset_y_pixels) * scale_factor

        motor_pulse_x = round(physical_x_mm * steps_per_mm_X)
        motor_pulse_y = round(physical_y_mm * steps_per_mm_Y)
        
        current_contour_points.append((motor_pulse_x, motor_pulse_y))

    # 存储处理后的轮廓数据
    processed_contours_data.append(current_contour_points)
    # 存储轮廓的起始点（映射后的电机脉冲坐标）
    contour_start_points_mapped.append(current_contour_points[0]) # 只取X, Y


print(f"原始轮廓处理后，得到 {len(processed_contours_data)} 个有效轮廓。")

#​ --- 5. 优化轮廓访问顺序 (TSP简化版 - 贪婪算法) ---
# 从 (0,0) 开始，每次找最近的未访问轮廓的起始点。
optimized_drawing_points = [] # 最终扁平化的所有绘制点
path_metadata_list = []       # 存储每个路径的元数据 (start_idx, length)

visited_contours = [False] * len(processed_contours_data)
current_x = 0 # 假定起始位置在物理原点 (0,0)
current_y = 0

total_points_count = 0 # 用于计算 flattened array 的总点数

for _ in range(len(processed_contours_data)): # 循环直到所有轮廓都被访问
    min_dist = float('inf')
    next_contour_idx = -1

    for i in range(len(processed_contours_data)):
        if not visited_contours[i]:
            # 计算当前位置到该轮廓起始点的距离
            cx, cy = contour_start_points_mapped[i]
            dist = (cx - current_x)**2 + (cy - current_y)**2
            if dist < min_dist:
                min_dist = dist
                next_contour_idx = i
    
    if next_contour_idx != -1:
        visited_contours[next_contour_idx] = True
        
        # 获取当前轮廓的所有点
        current_contour_points_list = processed_contours_data[next_contour_idx]
        
        # 记录路径元数据
        path_start_idx = total_points_count
        path_length = len(current_contour_points_list)
        path_metadata_list.append((path_start_idx, path_length))
        
        # 将选中的轮廓点添加到总列表中
        optimized_drawing_points.extend(current_contour_points_list)
        total_points_count += path_length

        # 更新当前位置为该轮廓的最后一个点 (以进行下一次最近点搜索)
        last_point_of_current_contour = current_contour_points_list[-1]
        current_x = last_point_of_current_contour[0]
        current_y = last_point_of_current_contour[1]
    else:
        # 没有找到下一个轮廓 (所有轮廓都已访问过，或列表为空)
        break

total_points_in_flat_array = total_points_count
print(f"经过轮廓优化排序后，总绘制点数为 {total_points_in_flat_array} 个。")
print(f"共生成 {len(path_metadata_list)} 个独立绘制路径。")


# --- 6. 生成 C 语言头文件 ---

# --- 生成 drawing_points 数组内容 ---
points_array_lines = []
for mx, my in optimized_drawing_points:
    points_array_lines.append(f"    {{{mx}, {my}}},")

# 移除最后一个点的逗号
if points_array_lines:
    points_array_lines[-1] = points_array_lines[-1].rstrip(',')

# --- 生成 path_metadata 数组内容 ---
metadata_array_lines = []
for start_idx, length in path_metadata_list:
    metadata_array_lines.append(f"    {{{start_idx}, {length}}},")

# 移除最后一个点的逗号
if metadata_array_lines:
    metadata_array_lines[-1] = metadata_array_lines[-1].rstrip(',')


# --- 构建完整的 C 语言头文件内容 ---
c_header_content = f"""
#ifndef __{points_array_name.upper()}_H__
#define __{points_array_name.upper()}_H__

#include <stdint.h> // For int32_t, uint16_t etc.

// 定义绘制点的总数 (包含所有轮廓的所有点)
#define NUM_DRAWING_POINTS {total_points_in_flat_array}

// 定义独立绘制路径的数量
#define NUM_DRAWING_PATHS {len(path_metadata_list)}

// 激光烧灼时间 (单位：毫秒) - 当激光开启并到位后，停留的时间。
// 对于直线绘制，这个值通常设置为0，因为激光是连续开启的。
// 只有在画点时才需要设置一个非零值。
#define LASER_BURN_DURATION_MS {laser_burn_duration_ms}

// 步进电机速度 (单位：脉冲/秒)
// 这需要根据​您的步进电机最大速度和希望的绘制速度来设定。
// 假设这里设定一个默认值，您可以在STM32代码中微调。
// 此速度将影响 Move_Abs_Position 的实现。
#define MOTOR_STEP_RATE_HZ 5000 // 比如5000脉冲/秒

// 绘制点数据二维数组
// 格式：{{{{X0, Y0}}, {{X1, Y1}}, ...}}
// 使用 'const' 将数组存储在Flash中以节省RAM
// 使用 'int32_t' 确保能存储正负和较大范围的脉冲值
const int32_t {points_array_name}[NUM_DRAWING_POINTS][2] = {{
"""

c_header_content += "\n".join(points_array_lines) # 将所有点行拼接起来

c_header_content += f"""
}};

// 路径元数据结构体
typedef struct {{
    uint16_t start_idx; // 在 drawing_points 数组中的起始索引
    uint16_t length;    // 该路径包含的点数量
}} PathInfo_t;

// 路径元数据数组
const PathInfo_t {metadata_array_name}[NUM_DRAWING_PATHS] = {{
"""

c_header_content += "\n".join(metadata_array_lines) # 将所有元数据行拼接起来

c_header_content += f"""
}};

#endif // __{points_array_name.upper()}_H__
"""

# 将内容写入文件
with open(output_c_array_file, "w", encoding="utf-8") as f:
    f.write(c_header_content)

print(f"\nSTM32 C语言头文件已生成到 '{output_c_array_file}'。")
print(f"数组 '{points_array_name}' 包含 {total_points_in_flat_array} 个点。")
print(f"数组 '{metadata_array_name}' 包含 {len(path_metadata_list)} 个路径元数据。")
print("下一步：将此文件添加到您的STM32工程中，并编写相应的控制代码。")

