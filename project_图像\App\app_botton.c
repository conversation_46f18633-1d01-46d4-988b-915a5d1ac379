#include "app_botton.h"

uint8_t key_val = 0;  // ��ǰ����״̬
uint8_t key_old = 0;  // ǰһ����״̬
uint8_t key_down = 0; // ���µİ���
uint8_t key_up = 0;	  // �ͷŵİ���

uint8_t flag = 0;

uint8_t cmd[16] = {0};
/**
 * @brief ��ȡ����״̬
 *
 * �ú�����ȡ������ GPIO �����ϵİ���״̬����������Ӧ�İ�����š�
 *
 * @return ���ذ�����š�0 ��ʾû�а������£�1-4 ��ʾ��Ӧ�İ��������¡�
 */
uint8_t key_read(void)
{
	// ���ڴ洢����״̬����ʱ����
	// uint8_t Action = 0;

	// HAL_GPIO_WritePin(ROW_0_GPIO_Port, ROW_0_Pin, GPIO_PIN_SET);

	// // ��� GPIOB ���� 0 ��״̬
	// if (HAL_GPIO_ReadPin(KEY_UP_GPIO_Port, KEY_UP_Pin) == GPIO_PIN_RESET)
	// 	Action = 1; // ���ϲ�

	// // ��� GPIOB ���� 1 ��״̬
	// if (HAL_GPIO_ReadPin(KEY_ENTER_GPIO_Port, KEY_ENTER_Pin) == GPIO_PIN_RESET)
	// 	Action = 2; // ȷ��

	// // ��� GPIOB ���� 2 ��״̬
	// if (HAL_GPIO_ReadPin(KEY_DOWN_GPIO_Port, KEY_DOWN_Pin) == GPIO_PIN_RESET)
	// 	Action = 3; // ���²�

	// // ���ؼ�⵽�İ������
	// return Action;
}

/**
 * @brief ������������
 *
 * �ú�������ɨ�谴����״̬�������°������º��ͷŵı�־
 */
void botton_task(MultiTimer *timer, void *userData)
{

	// ��ȡ��ǰ����״̬
	key_val = key_read();
	// ���㰴�µİ�������ǰ����״̬��ǰһ״̬��򣬲��뵱ǰ״̬���룩
	key_down = key_val & (key_old ^ key_val);
	// �����ͷŵİ�������ǰδ����״̬��ǰһ״̬��򣬲���ǰһ״̬���룩
	key_up = ~key_val & (key_old ^ key_val);
	// ����ǰһ����״̬
	key_old = key_val;
	//
	////	if(key_up == 1)//��ȡ��������
	////	{
	////		openmv_send(0xb1, 0);
	////	}                                                        600      3500
	////  if(key_up == 3)//��ȡ����״̬
	////	{
	////		openmv_send(0xb2, 0);
	////	}
	//	if(key_down == 1)//��ȡ��������
	//	{
	//		app_pencil_start();

	//	}
	//	if(key_down == 2)//��ȡ��������
	//	{
	//		flag = (++flag)%2;
	//	}
	//	if(key_down == 3)//��ȡ��������
	//	{
	//		app_vector_start();
	//	}

	multiTimerStart(&mt_botton, BOTTON_TASK_TIME, botton_task, NULL);
}
