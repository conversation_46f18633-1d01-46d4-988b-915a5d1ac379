/**
 * @file trajectory_example.c
 * @brief 轨迹运动使用示例
 * @copyright 白蛋电子工作室
 */

#include "app_laser_draw.h"
#include "app_uasrt.h"
#include "MultiTimer.h"

// 状态监控定时器
static MultiTimer mt_status_monitor;
static uint32_t last_progress = 0;

/**
 * @brief 状态监控任务函数
 */
void status_monitor_task(MultiTimer *timer, void *userData)
{
    if (get_trajectory_status())
    {
        uint32_t current_progress = get_trajectory_progress();
        if (current_progress != last_progress)
        {
            my_printf(&huart1, "运动进度: %d/813\r\n", current_progress);
            last_progress = current_progress;
        }
        // 继续监控
        multiTimerStart(&mt_status_monitor, 100, status_monitor_task, NULL);
    }
    else
    {
        my_printf(&huart1, "轨迹运动完成!\r\n");
    }
}

/**
 * @brief 轨迹运动使用示例(非阻塞)
 */
void trajectory_movement_example(void)
{
    // 初始化激光绘图系统
    app_laser_draw_init();

    my_printf(&huart1, "开始轨迹运动...\r\n");

    // 启动轨迹运动
    start_trajectory_movement();

    // 启动状态监控(非阻塞)
    last_progress = 0;
    multiTimerStart(&mt_status_monitor, 100, status_monitor_task, NULL);
}

/**
 * @brief 简单启动轨迹运动
 */
void start_trajectory_simple(void)
{
    // 初始化系统
    app_laser_draw_init();

    // 启动轨迹运动
    start_trajectory_movement();

    my_printf(&huart1, "轨迹运动已启动\r\n");
}
