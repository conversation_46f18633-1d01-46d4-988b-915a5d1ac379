// Copyright (c) 2024 白蛋电子工作室

#include "app_laser_draw.h"

// 数学常数定义
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 激光绘图状态变量
static laser_draw_state_t draw_state = LASER_DRAW_IDLE;
static bool laser_state = false;

// 当前绘图参数
static draw_function_t current_function;
static draw_params_t current_params;
static float current_progress = 0.0f;
static bool first_point = true;

// 绘图循环控制
static bool draw_loop_enabled = LASER_DRAW_LOOP_MODE;
static uint32_t draw_count = 0;

// 绘图定时器
static MultiTimer mt_laser_draw;

// 轨迹运动控制变量
static uint32_t current_segment_index = 0; // 当前运动段索引
static bool trajectory_running = false;    // 轨迹运动状态
static MultiTimer mt_trajectory;           // 轨迹运动定时器

/**
 * @brief 激光绘图系统初始化
 */
void app_laser_draw_init(void)
{
    // TODO: 初始化激光控制GPIO
    // GPIO_InitTypeDef GPIO_InitStruct = {0};
    // __HAL_RCC_GPIOB_CLK_ENABLE();
    // GPIO_InitStruct.Pin = LASER_GPIO_PIN;
    // GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    // GPIO_InitStruct.Pull = GPIO_NOPULL;
    // GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    // HAL_GPIO_Init(LASER_GPIO_PORT, &GPIO_InitStruct);

    // 初始状态关闭激光
    laser_off();

    // 初始化绘图状态
    draw_state = LASER_DRAW_IDLE;
}

/**
 * @brief 激光开启
 */
void laser_on(void)
{
    // TODO: 实际GPIO控制
    // HAL_GPIO_WritePin(LASER_GPIO_PORT, LASER_GPIO_PIN, GPIO_PIN_SET);
    laser_state = true;
}

/**
 * @brief 激光关闭
 */
void laser_off(void)
{
    // TODO: 实际GPIO控制
    // HAL_GPIO_WritePin(LASER_GPIO_PORT, LASER_GPIO_PIN, GPIO_PIN_RESET);
    laser_state = false;
}

/**
 * @brief 坐标转换为脉冲值
 * @param x X坐标(以纸张中心为原点)
 * @param y Y坐标(以纸张中心为原点)
 * @return 脉冲坐标点
 */
pulse_point_t convert_to_pulse(float x, float y)
{
    pulse_point_t pulse;

    // 将坐标转换为脉冲值(以纸张中心为原点)
    pulse.x = PAPER_CENTER_X + (int32_t)(x * LASER_COORDINATE_SCALE);
    pulse.y = PAPER_CENTER_Y + (int32_t)(y * LASER_COORDINATE_SCALE);

    return pulse;
}

/**
 * @brief 脉冲值转换为坐标
 * @param pulse_x X轴脉冲值
 * @param pulse_y Y轴脉冲值
 * @return 坐标点
 */
laser_point_t convert_to_coordinate(int32_t pulse_x, int32_t pulse_y)
{
    laser_point_t point;

    // 将脉冲值转换为坐标(以纸张中心为原点)
    point.x = (float)(pulse_x - PAPER_CENTER_X) / LASER_COORDINATE_SCALE;
    point.y = (float)(pulse_y - PAPER_CENTER_Y) / LASER_COORDINATE_SCALE;

    return point;
}

/**
 * @brief 移动到指定点(激光关闭)
 * @param x X坐标
 * @param y Y坐标
 */
void move_to_point(float x, float y)
{
    pulse_point_t pulse = convert_to_pulse(x, y);

    // 关闭激光
    laser_off();

    // 移动到目标位置
    Motor_Set_Position(pulse.x, pulse.y);
}

/**
 * @brief 绘制到指定点(激光开启)
 * @param x X坐标
 * @param y Y坐标
 */
void draw_to_point(float x, float y)
{
    pulse_point_t pulse = convert_to_pulse(x, y);

    // 开启激光
    laser_on();

    // 移动到目标位置
    Motor_Set_Position(pulse.x, pulse.y);
}

/**
 * @brief 绘制直线
 * @param start 起点
 * @param end 终点
 */
void draw_line(laser_point_t start, laser_point_t end)
{
    // 移动到起点
    move_to_point(start.x, start.y);

    // 绘制到终点
    draw_to_point(end.x, end.y);

    // 关闭激光
    laser_off();
}

/**
 * @brief 绘制矩形
 * @param corner 左下角坐标
 * @param width 宽度
 * @param height 高度
 */
void draw_rectangle(laser_point_t corner, float width, float height)
{
    laser_point_t points[4];

    // 计算四个角点
    points[0] = (laser_point_t){corner.x, corner.y};                  // 左下角
    points[1] = (laser_point_t){corner.x + width, corner.y};          // 右下角
    points[2] = (laser_point_t){corner.x + width, corner.y + height}; // 右上角
    points[3] = (laser_point_t){corner.x, corner.y + height};         // 左上角

    // 移动到起点
    move_to_point(points[0].x, points[0].y);

    // 开启激光绘制矩形
    laser_on();
    for (int i = 1; i < 4; i++)
    {
        draw_to_point(points[i].x, points[i].y);
    }
    // 回到起点闭合
    draw_to_point(points[0].x, points[0].y);

    // 关闭激光
    laser_off();
}

/**
 * @brief 绘制圆形
 * @param center 圆心
 * @param radius 半径
 */
void draw_circle(laser_point_t center, float radius)
{
    float step = 0.1f; // 角度步长
    bool first_point = true;

    for (float theta = 0; theta <= 2 * M_PI + step; theta += step)
    {
        float x = center.x + radius * cos(theta);
        float y = center.y + radius * sin(theta);

        if (first_point)
        {
            move_to_point(x, y);
            laser_on();
            first_point = false;
        }
        else
        {
            draw_to_point(x, y);
        }
    }

    // 关闭激光
    laser_off();
}

/**
 * @brief 绘制菱形(平行四边形，上下边水平，左右边倾斜)
 * @param center 菱形中心点
 * @param width 菱形宽度(上下边长度)
 * @param height 菱形高度(垂直高度)
 * @param skew 倾斜度(左右边的水平偏移量)
 */
void draw_diamond(laser_point_t center, float width, float height, float skew)
{
    laser_point_t points[4];

    // 计算菱形四个顶点坐标(上下边水平，左右边倾斜)
    points[0] = (laser_point_t){center.x - width / 2 + skew, center.y + height / 2}; // 左上顶点
    points[1] = (laser_point_t){center.x + width / 2 + skew, center.y + height / 2}; // 右上顶点
    points[2] = (laser_point_t){center.x + width / 2 - skew, center.y - height / 2}; // 右下顶点
    points[3] = (laser_point_t){center.x - width / 2 - skew, center.y - height / 2}; // 左下顶点

    // 移动到起点
    move_to_point(points[0].x, points[0].y);

    // 开启激光绘制菱形
    laser_on();
    for (int i = 1; i < 4; i++)
    {
        draw_to_point(points[i].x, points[i].y);
    }
    // 回到起点闭合
    draw_to_point(points[0].x, points[0].y);

    // 关闭激光
    laser_off();
}

/**
 * @brief 激光绘图任务函数
 * 由定时器回调函数调用，逐步执行绘图
 */
void app_laser_draw_task(MultiTimer *timer, void *userData)
{
    if (draw_state != LASER_DRAW_DRAWING)
    {
        return;
    }

    float x, y;
    bool draw_complete = false;

    // 根据函数类型计算当前点坐标
    if (current_function.func_xy != NULL)
    {
        // 直角坐标函数 y=f(x)
        if (current_progress <= current_params.x_max)
        {
            x = current_progress * current_params.scale;
            y = current_function.func_xy(current_progress) * current_params.scale;
        }
        else
        {
            draw_complete = true;
        }
    }
    else if (current_function.param_x != NULL && current_function.param_y != NULL)
    {
        // 参数方程 x=f(t), y=g(t)
        if (current_progress <= current_params.t_max)
        {
            x = current_function.param_x(current_progress) * current_params.scale;
            y = current_function.param_y(current_progress) * current_params.scale;
        }
        else
        {
            draw_complete = true;
        }
    }
    else if (current_function.polar_r != NULL)
    {
        // 极坐标函数 r=f(θ)
        if (current_progress <= current_params.theta_max)
        {
            float r = current_function.polar_r(current_progress) * current_params.scale;
            x = r * cos(current_progress);
            y = r * sin(current_progress);
        }
        else
        {
            draw_complete = true;
        }
    }

    // 执行绘图动作
    if (!draw_complete)
    {
        if (first_point)
        {
            move_to_point(x, y);
            first_point = false;
        }
        else
        {
            draw_to_point(x, y);
        }

        // 递增进度到下一个点
        current_progress += current_params.step;

        // 继续下一步绘制
        multiTimerStart(&mt_laser_draw, LASER_DRAW_STEP_DELAY, app_laser_draw_task, NULL);
    }
    else
    {
        // 绘制完成
        laser_off();
        draw_count++;

        // 检查是否需要循环绘制
        if (draw_loop_enabled)
        {
            // 重新开始绘制
            current_progress = (current_function.func_xy != NULL) ? current_params.x_min : (current_function.param_x != NULL) ? current_params.t_min
                                                                                                                              : current_params.theta_min;
            first_point = true;
            multiTimerStart(&mt_laser_draw, LASER_DRAW_STEP_DELAY, app_laser_draw_task, NULL);
        }
        else
        {
            draw_state = LASER_DRAW_IDLE; // 绘制完成后重置为空闲状态
        }
    }
}

/**
 * @brief 开始绘制函数图形
 * @param func 绘图函数结构体指针
 * @param params 绘图参数
 */
void start_drawing(draw_function_t *func, draw_params_t params)
{
    if (draw_state == LASER_DRAW_DRAWING)
    {
        return; // 如果正在绘制，直接返回
    }

    // 保存绘图参数
    current_function = *func;
    current_params = params;
    draw_count = 0;
    first_point = true;

    // 设置初始进度
    if (current_function.func_xy != NULL)
    {
        current_progress = current_params.x_min;
    }
    else if (current_function.param_x != NULL && current_function.param_y != NULL)
    {
        current_progress = current_params.t_min;
    }
    else if (current_function.polar_r != NULL)
    {
        current_progress = current_params.theta_min;
    }

    // 设置绘制状态
    draw_state = LASER_DRAW_DRAWING;

    // 启动绘图定时器
    multiTimerStart(&mt_laser_draw, LASER_DRAW_STEP_DELAY, app_laser_draw_task, NULL);
}

/**
 * @brief 停止绘制
 */
void stop_drawing(void)
{
    draw_state = LASER_DRAW_IDLE;
    laser_off(); // 关闭激光
}

/**
 * @brief 获取绘制状态
 * @return 当前绘制状态
 */
laser_draw_state_t get_draw_state(void)
{
    return draw_state;
}

/* 预定义常用数学函数 */

/**
 * @brief 直线函数 y = kx + b
 */
float line_function(float x)
{
    return 0.5f * x + 1.0f; // 示例：斜率0.5，截距1
}

/**
 * @brief 正弦函数 y = sin(x)
 */
float sine_function(float x)
{
    return sin(x);
}

/**
 * @brief 余弦函数 y = cos(x)
 */
float cosine_function(float x)
{
    return cos(x);
}

/**
 * @brief 抛物线函数 y = x²
 */
float parabola_function(float x)
{
    return x * x;
}

/**
 * @brief 圆的参数方程 - X分量
 */
float circle_param_x(float t)
{
    return cos(t);
}

/**
 * @brief 圆的参数方程 - Y分量
 */
float circle_param_y(float t)
{
    return sin(t);
}

/**
 * @brief 心形线极坐标函数 r = a(1-cos(θ))
 */
float heart_polar_r(float theta)
{
    return 1.0f - cos(theta);
}

/**
 * @brief 玫瑰花极坐标函数 r = cos(3θ)
 */
float rose_polar_r(float theta)
{
    return cos(3 * theta);
}

/**
 * @brief 阿基米德螺旋极坐标函数 r = aθ
 */
float spiral_polar_r(float theta)
{
    return 0.1f * theta;
}

/**
 * @brief 菱形参数方程 - X分量(平行四边形，上下边水平)
 * @param t 参数t，范围0到4
 */
float diamond_param_x(float t)
{
    float skew = 0.5f; // 倾斜度
    if (t <= 1.0f)
        return -1.0f + skew + 2.0f * t; // 上边：从(-1+skew,1)到(1+skew,1)
    else if (t <= 2.0f)
        return 1.0f + skew - 2.0f * skew * (t - 1.0f); // 右边：从(1+skew,1)到(1-skew,-1)
    else if (t <= 3.0f)
        return 1.0f - skew - 2.0f * (t - 2.0f); // 下边：从(1-skew,-1)到(-1-skew,-1)
    else
        return -1.0f - skew + 2.0f * skew * (t - 3.0f); // 左边：从(-1-skew,-1)到(-1+skew,1)
}

/**
 * @brief 菱形参数方程 - Y分量(平行四边形，上下边水平)
 * @param t 参数t，范围0到4
 */
float diamond_param_y(float t)
{
    if (t <= 1.0f)
        return 1.0f; // 上边：水平线y=1
    else if (t <= 2.0f)
        return 1.0f - 2.0f * (t - 1.0f); // 右边：从y=1到y=-1
    else if (t <= 3.0f)
        return -1.0f; // 下边：水平线y=-1
    else
        return -1.0f + 2.0f * (t - 3.0f); // 左边：从y=-1到y=1
}

/**
 * @brief 测试第一个点的坐标计算
 * @param func 绘图函数结构体指针
 * @param params 绘图参数
 */
void test_first_point(draw_function_t *func, draw_params_t params)
{
    float x, y;
    float test_progress;

    // 设置测试进度
    if (func->func_xy != NULL)
    {
        test_progress = params.x_min;
        x = test_progress * params.scale;
        y = func->func_xy(test_progress) * params.scale;
    }
    else if (func->param_x != NULL && func->param_y != NULL)
    {
        test_progress = params.t_min;
        x = func->param_x(test_progress) * params.scale;
        y = func->param_y(test_progress) * params.scale;
    }
    else if (func->polar_r != NULL)
    {
        test_progress = params.theta_min;
        float r = func->polar_r(test_progress) * params.scale;
        x = r * cos(test_progress);
        y = r * sin(test_progress);
    }

    // 转换为脉冲坐标
    pulse_point_t pulse = convert_to_pulse(x, y);

    // 这里可以添加调试输出或直接移动测试
    // 例如：printf("First point: x=%.2f, y=%.2f, pulse_x=%ld, pulse_y=%ld\n", x, y, pulse.x, pulse.y);

    // 直接移动到第一个点进行测试
    move_to_point(x, y);
}

#define NUM_DRAWING_POINTS 10330

const int32_t drawing_points[NUM_DRAWING_POINTS][2] = {
    {-215, -386},
    {-220, -381},
    {-225, -381},
    {-225, -376},
    {-225, -371},
    {-225, -376},
    {-220, -381},
    {-215, -376},
    {-215, -371},
    {-210, -366},
    {-210, -361},
    {-210, -356},
    {-210, -352},
    {-210, -347},
    {-205, -342},
    {-205, -337},
    {-205, -332},
    {-200, -327},
    {-200, -322},
    {-200, -317},
    {-200, -312},
    {-195, -308},
    {-195, -303},
    {-195, -298},
    {-190, -293},
    {-190, -288},
    {-190, -283},
    {-186, -278},
    {-186, -273},
    {-186, -269},
    {-186, -264},
    {-181, -259},
    {-181, -254},
    {-181, -249},
    {-176, -244},
    {-176, -239},
    {-176, -234},
    {-171, -229},
    {-171, -225},
    {-171, -220},
    {-171, -215},
    {-166, -210},
    {-166, -205},
    {-166, -200},
    {-161, -195},
    {-161, -190},
    {-161, -186},
    {-156, -181},
    {-156, -176},
    {-156, -171},
    {-156, -166},
    {-151, -161},
    {-151, -156},
    {-151, -151},
    {-146, -146},
    {-146, -142},
    {-146, -137},
    {-146, -132},
    {-142, -127},
    {-142, -122},
    {-142, -117},
    {-137, -112},
    {-137, -107},
    {-137, -103},
    {-132, -98},
    {-132, -93},
    {-132, -88},
    {-127, -83},
    {-127, -78},
    {-127, -73},
    {-127, -68},
    {-122, -63},
    {-122, -59},
    {-122, -54},
    {-117, -49},
    {-117, -44},
    {-117, -39},
    {-117, -34},
    {-112, -29},
    {-112, -24},
    {-112, -20},
    {-107, -15},
    {-107, -10},
    {-107, -5},
    {-103, 0},
    {-103, 5},
    {-98, 5},
    {-93, 5},
    {-88, 5},
    {-83, 5},
    {-78, 5},
    {-73, 5},
    {-68, 5},
    {-63, 5},
    {-59, 5},
    {-54, 5},
    {-49, 5},
    {-44, 5},
    {-39, 5},
    {-34, 5},
    {-29, 5},
    {-24, 5},
    {-20, 5},
    {-15, 5},
    {-10, 5},
    {-5, 5},
    {0, 5},
    {5, 5},
    {10, 5},
    {15, 5},
    {20, 5},
    {24, 5},
    {29, 5},
    {34, 5},
    {39, 5},
    {39, 0},
    {44, -5},
    {44, -10},
    {44, -15},
    {44, -20},
    {49, -24},
    {49, -29},
    {49, -34},
    {54, -39},
    {54, -44},
    {54, -49},
    {59, -54},
    {59, -59},
    {59, -63},
    {59, -68},
    {63, -73},
    {63, -78},
    {63, -83},
    {68, -88},
    {68, -93},
    {68, -98},
    {68, -103},
    {73, -107},
    {73, -112},
    {73, -117},
    {78, -122},
    {78, -127},
    {78, -132},
    {78, -137},
    {83, -142},
    {83, -146},
    {83, -151},
    {88, -156},
    {88, -161},
    {88, -166},
    {93, -171},
    {93, -176},
    {93, -181},
    {93, -186},
    {98, -190},
    {98, -195},
    {98, -200},
    {98, -205},
    {103, -210},
    {103, -215},
    {103, -220},
    {107, -225},
    {107, -229},
    {107, -234},
    {112, -239},
    {112, -244},
    {112, -249},
    {112, -254},
    {117, -259},
    {117, -264},
    {117, -269},
    {122, -273},
    {122, -278},
    {122, -283},
    {122, -288},
    {127, -293},
    {127, -298},
    {127, -303},
    {132, -308},
    {132, -312},
    {132, -317},
    {132, -322},
    {137, -327},
    {137, -332},
    {137, -337},
    {142, -342},
    {142, -347},
    {142, -352},
    {142, -356},
    {142, -361},
    {142, -366},
    {142, -361},
    {142, -356},
    {142, -352},
    {142, -347},
    {142, -342},
    {137, -337},
    {137, -332},
    {137, -327},
    {132, -322},
    {132, -317},
    {132, -312},
    {132, -308},
    {127, -303},
    {127, -298},
    {127, -293},
    {122, -288},
    {122, -283},
    {122, -278},
    {122, -273},
    {117, -269},
    {117, -264},
    {117, -259},
    {112, -254},
    {112, -249},
    {112, -244},
    {112, -239},
    {107, -234},
    {107, -229},
    {107, -225},
    {103, -220},
    {103, -215},
    {103, -210},
    {98, -205},
    {98, -200},
    {98, -195},
    {98, -190},
    {93, -186},
    {93, -181},
    {93, -176},
    {93, -171},
    {88, -166},
    {88, -161},
    {88, -156},
    {83, -151},
    {83, -146},
    {83, -142},
    {78, -137},
    {78, -132},
    {78, -127},
    {78, -122},
    {73, -117},
    {73, -112},
    {73, -107},
    {68, -103},
    {68, -98},
    {68, -93},
    {68, -88},
    {63, -83},
    {63, -78},
    {63, -73},
    {59, -68},
    {59, -63},
    {59, -59},
    {59, -54},
    {54, -49},
    {54, -44},
    {54, -39},
    {49, -34},
    {49, -29},
    {49, -24},
    {44, -20},
    {44, -15},
    {44, -10},
    {44, -5},
    {39, 0},
    {34, 5},
    {29, 5},
    {24, 5},
    {20, 5},
    {15, 5},
    {10, 5},
    {5, 5},
    {0, 5},
    {-5, 5},
    {-10, 5},
    {-15, 5},
    {-20, 5},
    {-24, 5},
    {-29, 5},
    {-34, 5},
    {-39, 5},
    {-44, 5},
    {-49, 5},
    {-54, 5},
    {-59, 5},
    {-63, 5},
    {-68, 5},
    {-73, 5},
    {-78, 5},
    {-83, 5},
    {-88, 5},
    {-93, 5},
    {-98, 5},
    {-103, 0},
    {-107, -5},
    {-107, -10},
    {-107, -15},
    {-112, -20},
    {-112, -24},
    {-112, -29},
    {-117, -34},
    {-117, -39},
    {-117, -44},
    {-117, -49},
    {-122, -54},
    {-122, -59},
    {-122, -63},
    {-127, -68},
    {-127, -73},
    {-127, -78},
    {-127, -83},
    {-132, -88},
    {-132, -93},
    {-132, -98},
    {-137, -103},
    {-137, -107},
    {-137, -112},
    {-142, -117},
    {-142, -122},
    {-142, -127},
    {-146, -132},
    {-146, -137},
    {-146, -142},
    {-146, -146},
    {-151, -151},
    {-151, -156},
    {-151, -161},
    {-156, -166},
    {-156, -171},
    {-156, -176},
    {-156, -181},
    {-161, -186},
    {-161, -190},
    {-161, -195},
    {-166, -200},
    {-166, -205},
    {-166, -210},
    {-171, -215},
    {-171, -220},
    {-171, -225},
    {-171, -229},
    {-176, -234},
    {-176, -239},
    {-176, -244},
    {-181, -249},
    {-181, -254},
    {-181, -259},
    {-186, -264},
    {-186, -269},
    {-186, -273},
    {-186, -278},
    {-190, -283},
    {-190, -288},
    {-190, -293},
    {-195, -298},
    {-195, -303},
    {-195, -308},
    {-200, -312},
    {-200, -317},
    {-200, -322},
    {-200, -327},
    {-205, -332},
    {-205, -337},
    {-205, -342},
    {-210, -347},
    {-210, -352},
    {-210, -356},
    {-210, -361},
    {-210, -366},
    {-215, -371},
    {-215, -376},
    {-215, -381},
    {-366, -532},
    {-366, -527},
    {-366, -522},
    {-366, -518},
    {-366, -513},
    {-366, -508},
    {-366, -503},
    {-366, -498},
    {-366, -493},
    {-366, -488},
    {-366, -483},
    {-366, -479},
    {-366, -474},
    {-366, -469},
    {-366, -464},
    {-366, -459},
    {-366, -454},
    {-366, -449},
    {-366, -444},
    {-366, -439},
    {-366, -435},
    {-366, -430},
    {-366, -425},
    {-366, -420},
    {-366, -415},
    {-366, -410},
    {-366, -405},
    {-366, -400},
    {-366, -396},
    {-366, -391},
    {-366, -386},
    {-366, -381},
    {-366, -376},
    {-366, -371},
    {-366, -366},
    {-366, -361},
    {-366, -356},
    {-366, -352},
    {-366, -347},
    {-366, -342},
    {-366, -337},
    {-366, -332},
    {-366, -327},
    {-366, -322},
    {-366, -317},
    {-366, -312},
    {-366, -308},
    {-366, -303},
    {-366, -298},
    {-366, -293},
    {-366, -288},
    {-366, -283},
    {-366, -278},
    {-366, -273},
    {-366, -269},
    {-366, -264},
    {-366, -259},
    {-366, -254},
    {-366, -249},
    {-366, -244},
    {-366, -239},
    {-366, -234},
    {-366, -229},
    {-366, -225},
    {-366, -220},
    {-366, -215},
    {-366, -210},
    {-366, -205},
    {-366, -200},
    {-366, -195},
    {-366, -190},
    {-366, -186},
    {-366, -181},
    {-366, -176},
    {-366, -171},
    {-366, -166},
    {-366, -161},
    {-366, -156},
    {-366, -151},
    {-366, -146},
    {-366, -142},
    {-366, -137},
    {-366, -132},
    {-366, -127},
    {-366, -122},
    {-366, -117},
    {-366, -112},
    {-366, -107},
    {-366, -103},
    {-366, -98},
    {-366, -93},
    {-366, -88},
    {-366, -83},
    {-366, -78},
    {-366, -73},
    {-366, -68},
    {-366, -63},
    {-366, -59},
    {-366, -54},
    {-366, -49},
    {-366, -44},
    {-361, -39},
    {-361, -34},
    {-361, -29},
    {-361, -24},
    {-366, -20},
    {-366, -15},
    {-366, -10},
    {-366, -5},
    {-366, 0},
    {-366, 5},
    {-366, 10},
    {-366, 15},
    {-366, 20},
    {-366, 24},
    {-366, 29},
    {-366, 34},
    {-366, 39},
    {-366, 44},
    {-366, 49},
    {-366, 54},
    {-366, 59},
    {-366, 63},
    {-361, 68},
    {-356, 68},
    {-352, 68},
    {-347, 68},
    {-342, 68},
    {-337, 68},
    {-332, 68},
    {-327, 68},
    {-322, 68},
    {-317, 68},
    {-312, 68},
    {-308, 68},
    {-303, 68},
    {-298, 68},
    {-293, 68},
    {-288, 68},
    {-283, 68},
    {-278, 68},
    {-273, 68},
    {-269, 68},
    {-264, 68},
    {-259, 68},
    {-254, 68},
    {-249, 68},
    {-244, 68},
    {-239, 68},
    {-234, 68},
    {-229, 68},
    {-225, 68},
    {-220, 68},
    {-220, 63},
    {-220, 59},
    {-220, 54},
    {-220, 49},
    {-220, 44},
    {-220, 39},
    {-220, 34},
    {-220, 29},
    {-220, 24},
    {-220, 20},
    {-220, 15},
    {-220, 10},
    {-220, 5},
    {-220, 0},
    {-220, -5},
    {-220, -10},
    {-220, -15},
    {-220, -20},
    {-220, -24},
    {-220, -29},
    {-220, -34},
    {-220, -39},
    {-220, -44},
    {-220, -49},
    {-220, -54},
    {-220, -59},
    {-220, -63},
    {-220, -68},
    {-220, -73},
    {-220, -78},
    {-220, -83},
    {-220, -88},
    {-220, -93},
    {-220, -98},
    {-220, -103},
    {-220, -107},
    {-220, -112},
    {-220, -117},
    {-220, -122},
    {-220, -127},
    {-220, -132},
    {-220, -137},
    {-220, -142},
    {-220, -146},
    {-220, -151},
    {-220, -156},
    {-220, -161},
    {-220, -166},
    {-220, -171},
    {-220, -176},
    {-220, -181},
    {-220, -186},
    {-220, -190},
    {-220, -195},
    {-220, -200},
    {-220, -205},
    {-220, -210},
    {-220, -215},
    {-220, -220},
    {-220, -225},
    {-220, -229},
    {-220, -234},
    {-220, -239},
    {-220, -244},
    {-220, -249},
    {-220, -254},
    {-220, -259},
    {-220, -264},
    {-220, -269},
    {-220, -273},
    {-220, -278},
    {-220, -283},
    {-220, -288},
    {-220, -293},
    {-220, -298},
    {-220, -303},
    {-220, -308},
    {-220, -312},
    {-220, -317},
    {-220, -322},
    {-220, -327},
    {-220, -332},
    {-220, -337},
    {-220, -342},
    {-220, -347},
    {-220, -352},
    {-220, -356},
    {-220, -361},
    {-220, -356},
    {-220, -352},
    {-220, -347},
    {-220, -342},
    {-220, -337},
    {-220, -332},
    {-220, -327},
    {-220, -322},
    {-220, -317},
    {-220, -312},
    {-220, -308},
    {-220, -303},
    {-220, -298},
    {-220, -293},
    {-220, -288},
    {-220, -283},
    {-220, -278},
    {-220, -273},
    {-220, -269},
    {-220, -264},
    {-220, -259},
    {-220, -254},
    {-220, -249},
    {-220, -244},
    {-220, -239},
    {-220, -234},
    {-220, -229},
    {-220, -225},
    {-220, -220},
    {-220, -215},
    {-220, -210},
    {-220, -205},
    {-220, -200},
    {-220, -195},
    {-220, -190},
    {-220, -186},
    {-220, -181},
    {-220, -176},
    {-220, -171},
    {-220, -166},
    {-220, -161},
    {-220, -156},
    {-220, -151},
    {-220, -146},
    {-220, -142},
    {-220, -137},
    {-220, -132},
    {-220, -127},
    {-220, -122},
    {-220, -117},
    {-220, -112},
    {-220, -107},
    {-220, -103},
    {-220, -98},
    {-220, -93},
    {-220, -88},
    {-220, -83},
    {-220, -78},
    {-220, -73},
    {-220, -68},
    {-220, -63},
    {-220, -59},
    {-220, -54},
    {-220, -49},
    {-220, -44},
    {-220, -39},
    {-220, -34},
    {-220, -29},
    {-220, -24},
    {-220, -20},
    {-220, -15},
    {-220, -10},
    {-220, -5},
    {-220, 0},
    {-220, 5},
    {-220, 10},
    {-220, 15},
    {-220, 20},
    {-220, 24},
    {-220, 29},
    {-220, 34},
    {-220, 39},
    {-220, 44},
    {-220, 49},
    {-220, 54},
    {-220, 59},
    {-220, 63},
    {-225, 68},
    {-229, 68},
    {-234, 68},
    {-239, 68},
    {-244, 68},
    {-249, 68},
    {-254, 68},
    {-259, 68},
    {-264, 68},
    {-269, 68},
    {-273, 68},
    {-278, 68},
    {-283, 68},
    {-288, 68},
    {-293, 68},
    {-298, 68},
    {-303, 68},
    {-308, 68},
    {-312, 68},
    {-317, 68},
    {-322, 68},
    {-327, 68},
    {-332, 68},
    {-337, 68},
    {-342, 68},
    {-347, 68},
    {-352, 68},
    {-356, 68},
    {-361, 68},
    {-366, 63},
    {-366, 59},
    {-366, 54},
    {-366, 49},
    {-366, 44},
    {-366, 39},
    {-366, 34},
    {-366, 29},
    {-366, 24},
    {-366, 20},
    {-366, 15},
    {-366, 10},
    {-366, 5},
    {-366, 0},
    {-366, -5},
    {-366, -10},
    {-366, -15},
    {-366, -20},
    {-361, -24},
    {-361, -29},
    {-361, -34},
    {-361, -39},
    {-366, -44},
    {-366, -49},
    {-366, -54},
    {-366, -59},
    {-366, -63},
    {-366, -68},
    {-366, -73},
    {-366, -78},
    {-366, -83},
    {-366, -88},
    {-366, -93},
    {-366, -98},
    {-366, -103},
    {-366, -107},
    {-366, -112},
    {-366, -117},
    {-366, -122},
    {-366, -127},
    {-366, -132},
    {-366, -137},
    {-366, -142},
    {-366, -146},
    {-366, -151},
    {-366, -156},
    {-366, -161},
    {-366, -166},
    {-366, -171},
    {-366, -176},
    {-366, -181},
    {-366, -186},
    {-366, -190},
    {-366, -195},
    {-366, -200},
    {-366, -205},
    {-366, -210},
    {-366, -215},
    {-366, -220},
    {-366, -225},
    {-366, -229},
    {-366, -234},
    {-366, -239},
    {-366, -244},
    {-366, -249},
    {-366, -254},
    {-366, -259},
    {-366, -264},
    {-366, -269},
    {-366, -273},
    {-366, -278},
    {-366, -283},
    {-366, -288},
    {-366, -293},
    {-366, -298},
    {-366, -303},
    {-366, -308},
    {-366, -312},
    {-366, -317},
    {-366, -322},
    {-366, -327},
    {-366, -332},
    {-366, -337},
    {-366, -342},
    {-366, -347},
    {-366, -352},
    {-366, -356},
    {-366, -361},
    {-366, -366},
    {-366, -371},
    {-366, -376},
    {-366, -381},
    {-366, -386},
    {-366, -391},
    {-366, -396},
    {-366, -400},
    {-366, -405},
    {-366, -410},
    {-366, -415},
    {-366, -420},
    {-366, -425},
    {-366, -430},
    {-366, -435},
    {-366, -439},
    {-366, -444},
    {-366, -449},
    {-366, -454},
    {-366, -459},
    {-366, -464},
    {-366, -469},
    {-366, -474},
    {-366, -479},
    {-366, -483},
    {-366, -488},
    {-366, -493},
    {-366, -498},
    {-366, -503},
    {-366, -508},
    {-366, -513},
    {-366, -518},
    {-366, -522},
    {-366, -527},
    {-361, -532},
    {-356, -532},
    {-352, -532},
    {-347, -532},
    {-342, -532},
    {-337, -532},
    {-332, -532},
    {-327, -532},
    {-322, -532},
    {-317, -532},
    {-312, -532},
    {-308, -532},
    {-303, -532},
    {-298, -532},
    {-293, -532},
    {-288, -532},
    {-283, -532},
    {-278, -532},
    {-273, -532},
    {-269, -532},
    {-264, -532},
    {-259, -532},
    {-254, -532},
    {-249, -532},
    {-244, -532},
    {-239, -532},
    {-234, -532},
    {-229, -532},
    {-225, -532},
    {-220, -532},
    {-215, -532},
    {-210, -532},
    {-205, -532},
    {-200, -532},
    {-195, -532},
    {-190, -532},
    {-186, -532},
    {-181, -532},
    {-176, -532},
    {-171, -532},
    {-166, -532},
    {-161, -532},
    {-156, -532},
    {-151, -532},
    {-146, -532},
    {-142, -532},
    {-137, -532},
    {-132, -527},
    {-127, -522},
    {-127, -518},
    {-127, -513},
    {-122, -508},
    {-122, -503},
    {-122, -498},
    {-122, -493},
    {-117, -488},
    {-117, -483},
    {-117, -479},
    {-117, -474},
    {-112, -469},
    {-112, -464},
    {-112, -459},
    {-107, -454},
    {-107, -449},
    {-107, -444},
    {-107, -439},
    {-103, -435},
    {-103, -430},
    {-103, -425},
    {-98, -420},
    {-98, -415},
    {-98, -410},
    {-98, -405},
    {-93, -400},
    {-93, -396},
    {-93, -391},
    {-88, -386},
    {-88, -381},
    {-88, -376},
    {-88, -371},
    {-83, -366},
    {-83, -361},
    {-83, -356},
    {-78, -352},
    {-78, -347},
    {-78, -342},
    {-78, -337},
    {-73, -332},
    {-73, -327},
    {-73, -322},
    {-68, -317},
    {-68, -312},
    {-68, -308},
    {-68, -303},
    {-63, -298},
    {-63, -293},
    {-63, -288},
    {-59, -283},
    {-59, -278},
    {-59, -273},
    {-59, -269},
    {-54, -264},
    {-54, -259},
    {-54, -254},
    {-49, -249},
    {-49, -244},
    {-49, -239},
    {-44, -234},
    {-44, -229},
    {-44, -225},
    {-44, -220},
    {-39, -215},
    {-39, -210},
    {-39, -205},
    {-39, -200},
    {-39, -205},
    {-39, -210},
    {-39, -215},
    {-44, -220},
    {-44, -225},
    {-44, -229},
    {-44, -234},
    {-49, -239},
    {-49, -244},
    {-49, -249},
    {-54, -254},
    {-54, -259},
    {-54, -264},
    {-59, -269},
    {-59, -273},
    {-59, -278},
    {-59, -283},
    {-63, -288},
    {-63, -293},
    {-63, -298},
    {-68, -303},
    {-68, -308},
    {-68, -312},
    {-68, -317},
    {-73, -322},
    {-73, -327},
    {-73, -332},
    {-78, -337},
    {-78, -342},
    {-78, -347},
    {-78, -352},
    {-83, -356},
    {-83, -361},
    {-83, -366},
    {-88, -371},
    {-88, -376},
    {-88, -381},
    {-88, -386},
    {-93, -391},
    {-93, -396},
    {-93, -400},
    {-98, -405},
    {-98, -410},
    {-98, -415},
    {-98, -420},
    {-103, -425},
    {-103, -430},
    {-103, -435},
    {-107, -439},
    {-107, -444},
    {-107, -449},
    {-107, -454},
    {-112, -459},
    {-112, -464},
    {-112, -469},
    {-117, -474},
    {-117, -479},
    {-117, -483},
    {-117, -488},
    {-122, -493},
    {-122, -498},
    {-122, -503},
    {-122, -508},
    {-127, -513},
    {-127, -518},
    {-127, -522},
    {-132, -527},
    {-132, -532},
    {-137, -532},
    {-142, -532},
    {-146, -532},
    {-151, -532},
    {-156, -532},
    {-161, -532},
    {-166, -532},
    {-171, -532},
    {-176, -532},
    {-181, -532},
    {-186, -532},
    {-190, -532},
    {-195, -532},
    {-200, -532},
    {-205, -532},
    {-210, -532},
    {-215, -532},
    {-220, -532},
    {-225, -532},
    {-229, -532},
    {-234, -532},
    {-239, -532},
    {-244, -532},
    {-249, -532},
    {-254, -532},
    {-259, -532},
    {-264, -532},
    {-269, -532},
    {-273, -532},
    {-278, -532},
    {-283, -532},
    {-288, -532},
    {-293, -532},
    {-298, -532},
    {-303, -532},
    {-308, -532},
    {-312, -532},
    {-317, -532},
    {-322, -532},
    {-327, -532},
    {-332, -532},
    {-337, -532},
    {-342, -532},
    {-347, -532},
    {-352, -532},
    {-356, -532},
    {-361, -532},
    {59, -532},
    {59, -527},
    {59, -522},
    {59, -518},
    {54, -513},
    {54, -508},
    {54, -503},
    {54, -498},
    {49, -493},
    {49, -488},
    {49, -483},
    {49, -479},
    {44, -474},
    {44, -469},
    {44, -464},
    {44, -459},
    {39, -454},
    {39, -449},
    {39, -444},
    {39, -439},
    {34, -435},
    {34, -430},
    {34, -425},
    {29, -420},
    {29, -415},
    {29, -410},
    {29, -405},
    {24, -400},
    {24, -396},
    {24, -391},
    {24, -386},
    {20, -381},
    {20, -376},
    {20, -371},
    {15, -366},
    {15, -361},
    {15, -356},
    {15, -352},
    {10, -347},
    {10, -342},
    {10, -337},
    {5, -332},
    {5, -327},
    {5, -322},
    {5, -317},
    {0, -312},
    {0, -308},
    {0, -303},
    {0, -298},
    {-5, -293},
    {-5, -288},
    {-5, -283},
    {-10, -278},
    {-10, -273},
    {-10, -269},
    {-10, -264},
    {-15, -259},
    {-15, -254},
    {-15, -249},
    {-15, -244},
    {-20, -239},
    {-20, -234},
    {-20, -229},
    {-24, -225},
    {-24, -220},
    {-24, -215},
    {-24, -210},
    {-29, -205},
    {-29, -200},
    {-29, -195},
    {-29, -200},
    {-29, -205},
    {-24, -210},
    {-24, -215},
    {-24, -220},
    {-24, -225},
    {-20, -229},
    {-20, -234},
    {-20, -239},
    {-15, -244},
    {-15, -249},
    {-15, -254},
    {-15, -259},
    {-10, -264},
    {-10, -269},
    {-10, -273},
    {-10, -278},
    {-5, -283},
    {-5, -288},
    {-5, -293},
    {0, -298},
    {0, -303},
    {0, -308},
    {0, -312},
    {5, -317},
    {5, -322},
    {5, -327},
    {5, -332},
    {10, -337},
    {10, -342},
    {10, -347},
    {15, -352},
    {15, -356},
    {15, -361},
    {15, -366},
    {20, -371},
    {20, -376},
    {20, -381},
    {24, -386},
    {24, -391},
    {24, -396},
    {24, -400},
    {29, -405},
    {29, -410},
    {29, -415},
    {29, -420},
    {34, -425},
    {34, -430},
    {34, -435},
    {39, -439},
    {39, -444},
    {39, -449},
    {39, -454},
    {44, -459},
    {44, -464},
    {44, -469},
    {44, -474},
    {49, -479},
    {49, -483},
    {49, -488},
    {49, -493},
    {54, -498},
    {54, -503},
    {54, -508},
    {54, -513},
    {59, -518},
    {59, -522},
    {59, -527},
    {63, -532},
    {68, -532},
    {73, -532},
    {78, -532},
    {83, -532},
    {88, -532},
    {93, -532},
    {98, -532},
    {103, -532},
    {107, -532},
    {112, -532},
    {117, -532},
    {122, -532},
    {127, -532},
    {132, -532},
    {137, -532},
    {142, -532},
    {146, -532},
    {151, -532},
    {156, -532},
    {161, -532},
    {166, -532},
    {171, -532},
    {176, -532},
    {181, -532},
    {186, -532},
    {190, -532},
    {195, -532},
    {200, -532},
    {205, -532},
    {210, -532},
    {215, -532},
    {220, -532},
    {225, -532},
    {229, -532},
    {234, -532},
    {239, -532},
    {244, -532},
    {249, -532},
    {254, -532},
    {259, -532},
    {264, -532},
    {269, -532},
    {273, -532},
    {278, -532},
    {283, -532},
    {288, -532},
    {293, -532},
    {298, -527},
    {298, -522},
    {298, -518},
    {298, -513},
    {298, -508},
    {298, -503},
    {298, -498},
    {298, -493},
    {298, -488},
    {298, -483},
    {298, -479},
    {298, -474},
    {298, -469},
    {298, -464},
    {298, -459},
    {298, -454},
    {298, -449},
    {298, -444},
    {298, -439},
    {298, -435},
    {298, -430},
    {298, -425},
    {298, -420},
    {298, -415},
    {298, -410},
    {298, -405},
    {298, -400},
    {298, -396},
    {298, -391},
    {298, -386},
    {298, -381},
    {298, -376},
    {298, -371},
    {298, -366},
    {298, -361},
    {298, -356},
    {298, -352},
    {298, -347},
    {298, -342},
    {298, -337},
    {298, -332},
    {298, -327},
    {298, -322},
    {298, -317},
    {298, -312},
    {298, -308},
    {298, -303},
    {298, -298},
    {298, -293},
    {298, -288},
    {298, -283},
    {298, -278},
    {298, -273},
    {298, -269},
    {298, -264},
    {298, -259},
    {298, -254},
    {298, -249},
    {298, -244},
    {298, -239},
    {298, -234},
    {298, -229},
    {298, -225},
    {298, -220},
    {298, -215},
    {298, -210},
    {298, -205},
    {298, -200},
    {298, -195},
    {298, -190},
    {298, -186},
    {298, -181},
    {298, -176},
    {298, -171},
    {298, -166},
    {298, -161},
    {298, -156},
    {298, -151},
    {298, -146},
    {298, -142},
    {298, -137},
    {298, -132},
    {298, -127},
    {298, -122},
    {298, -117},
    {298, -112},
    {298, -107},
    {298, -103},
    {298, -98},
    {298, -93},
    {298, -88},
    {298, -83},
    {298, -78},
    {298, -73},
    {298, -68},
    {298, -63},
    {298, -59},
    {298, -54},
    {298, -49},
    {298, -44},
    {298, -39},
    {298, -34},
    {298, -29},
    {298, -24},
    {298, -20},
    {298, -15},
    {298, -10},
    {298, -5},
    {298, 0},
    {298, 5},
    {298, 10},
    {298, 15},
    {298, 20},
    {298, 24},
    {298, 29},
    {298, 34},
    {298, 39},
    {298, 44},
    {298, 49},
    {298, 54},
    {298, 59},
    {298, 63},
    {293, 68},
    {288, 68},
    {283, 68},
    {278, 68},
    {273, 68},
    {269, 68},
    {264, 68},
    {259, 68},
    {254, 68},
    {249, 68},
    {244, 68},
    {239, 68},
    {234, 68},
    {229, 68},
    {225, 68},
    {220, 68},
    {215, 68},
    {210, 68},
    {205, 68},
    {200, 68},
    {195, 68},
    {190, 68},
    {186, 68},
    {181, 68},
    {176, 68},
    {171, 68},
    {166, 68},
    {161, 68},
    {156, 68},
    {151, 63},
    {151, 59},
    {151, 54},
    {151, 49},
    {151, 44},
    {151, 39},
    {151, 34},
    {151, 29},
    {151, 24},
    {151, 20},
    {151, 15},
    {151, 10},
    {151, 5},
    {151, 0},
    {151, -5},
    {151, -10},
    {151, -15},
    {151, -20},
    {151, -24},
    {151, -29},
    {151, -34},
    {151, -39},
    {151, -44},
    {151, -49},
    {151, -54},
    {151, -59},
    {151, -63},
    {151, -68},
    {151, -73},
    {151, -78},
    {151, -83},
    {151, -88},
    {151, -93},
    {151, -98},
    {151, -103},
    {151, -107},
    {151, -112},
    {151, -117},
    {151, -122},
    {151, -127},
    {151, -132},
    {151, -137},
    {151, -142},
    {151, -146},
    {151, -151},
    {151, -156},
    {151, -161},
    {151, -166},
    {151, -171},
    {151, -176},
    {151, -181},
    {151, -186},
    {151, -190},
    {151, -195},
    {151, -200},
    {151, -205},
    {151, -210},
    {151, -215},
    {151, -220},
    {151, -225},
    {151, -229},
    {151, -234},
    {151, -239},
    {151, -244},
    {151, -249},
    {151, -254},
    {151, -259},
    {151, -264},
    {151, -269},
    {151, -273},
    {151, -278},
    {151, -283},
    {151, -288},
    {151, -293},
    {151, -298},
    {151, -303},
    {151, -308},
    {151, -312},
    {151, -317},
    {151, -322},
    {151, -327},
    {151, -332},
    {151, -337},
    {151, -342},
    {151, -347},
    {151, -352},
    {151, -356},
    {151, -361},
    {151, -366},
    {151, -361},
    {151, -356},
    {151, -352},
    {151, -347},
    {151, -342},
    {151, -337},
    {151, -332},
    {151, -327},
    {151, -322},
    {151, -317},
    {151, -312},
    {151, -308},
    {151, -303},
    {151, -298},
    {151, -293},
    {151, -288},
    {151, -283},
    {151, -278},
    {151, -273},
    {151, -269},
    {151, -264},
    {151, -259},
    {151, -254},
    {151, -249},
    {151, -244},
    {151, -239},
    {151, -234},
    {151, -229},
    {151, -225},
    {151, -220},
    {151, -215},
    {151, -210},
    {151, -205},
    {151, -200},
    {151, -195},
    {151, -190},
    {151, -186},
    {151, -181},
    {151, -176},
    {151, -171},
    {151, -166},
    {151, -161},
    {151, -156},
    {151, -151},
    {151, -146},
    {151, -142},
    {151, -137},
    {151, -132},
    {151, -127},
    {151, -122},
    {151, -117},
    {151, -112},
    {151, -107},
    {151, -103},
    {151, -98},
    {151, -93},
    {151, -88},
    {151, -83},
    {151, -78},
    {151, -73},
    {151, -68},
    {151, -63},
    {151, -59},
    {151, -54},
    {151, -49},
    {151, -44},
    {151, -39},
    {151, -34},
    {151, -29},
    {151, -24},
    {151, -20},
    {151, -15},
    {151, -10},
    {151, -5},
    {151, 0},
    {151, 5},
    {151, 10},
    {151, 15},
    {151, 20},
    {151, 24},
    {151, 29},
    {151, 34},
    {151, 39},
    {151, 44},
    {151, 49},
    {151, 54},
    {151, 59},
    {151, 63},
    {151, 68},
    {156, 68},
    {161, 68},
    {166, 68},
    {171, 68},
    {176, 68},
    {181, 68},
    {186, 68},
    {190, 68},
    {195, 68},
    {200, 68},
    {205, 68},
    {210, 68},
    {215, 68},
    {220, 68},
    {225, 68},
    {229, 68},
    {234, 68},
    {239, 68},
    {244, 68},
    {249, 68},
    {254, 68},
    {259, 68},
    {264, 68},
    {269, 68},
    {273, 68},
    {278, 68},
    {283, 68},
    {288, 68},
    {293, 68},
    {298, 68},
    {298, 63},
    {298, 59},
    {298, 54},
    {298, 49},
    {298, 44},
    {298, 39},
    {298, 34},
    {298, 29},
    {298, 24},
    {298, 20},
    {298, 15},
    {298, 10},
    {298, 5},
    {298, 0},
    {298, -5},
    {298, -10},
    {298, -15},
    {298, -20},
    {298, -24},
    {298, -29},
    {298, -34},
    {298, -39},
    {298, -44},
    {298, -49},
    {298, -54},
    {298, -59},
    {298, -63},
    {298, -68},
    {298, -73},
    {298, -78},
    {298, -83},
    {298, -88},
    {298, -93},
    {298, -98},
    {298, -103},
    {298, -107},
    {298, -112},
    {298, -117},
    {298, -122},
    {298, -127},
    {298, -132},
    {298, -137},
    {298, -142},
    {298, -146},
    {298, -151},
    {298, -156},
    {298, -161},
    {298, -166},
    {298, -171},
    {298, -176},
    {298, -181},
    {298, -186},
    {298, -190},
    {298, -195},
    {298, -200},
    {298, -205},
    {298, -210},
    {298, -215},
    {298, -220},
    {298, -225},
    {298, -229},
    {298, -234},
    {298, -239},
    {298, -244},
    {298, -249},
    {298, -254},
    {298, -259},
    {298, -264},
    {298, -269},
    {298, -273},
    {298, -278},
    {298, -283},
    {298, -288},
    {298, -293},
    {298, -298},
    {298, -303},
    {298, -308},
    {298, -312},
    {298, -317},
    {298, -322},
    {298, -327},
    {298, -332},
    {298, -337},
    {298, -342},
    {298, -347},
    {298, -352},
    {298, -356},
    {298, -361},
    {298, -366},
    {298, -371},
    {298, -376},
    {298, -381},
    {298, -386},
    {298, -391},
    {298, -396},
    {298, -400},
    {298, -405},
    {298, -410},
    {298, -415},
    {298, -420},
    {298, -425},
    {298, -430},
    {298, -435},
    {298, -439},
    {298, -444},
    {298, -449},
    {298, -454},
    {298, -459},
    {298, -464},
    {298, -469},
    {298, -474},
    {298, -479},
    {298, -483},
    {298, -488},
    {298, -493},
    {298, -498},
    {298, -503},
    {298, -508},
    {298, -513},
    {298, -518},
    {298, -522},
    {298, -527},
    {298, -532},
    {293, -532},
    {288, -532},
    {283, -532},
    {278, -532},
    {273, -532},
    {269, -532},
    {264, -532},
    {259, -532},
    {254, -532},
    {249, -532},
    {244, -532},
    {239, -532},
    {234, -532},
    {229, -532},
    {225, -532},
    {220, -532},
    {215, -532},
    {210, -532},
    {205, -532},
    {200, -532},
    {195, -532},
    {190, -532},
    {186, -532},
    {181, -532},
    {176, -532},
    {171, -532},
    {166, -532},
    {161, -532},
    {156, -532},
    {151, -532},
    {146, -532},
    {142, -532},
    {137, -532},
    {132, -532},
    {127, -532},
    {122, -532},
    {117, -532},
    {112, -532},
    {107, -532},
    {103, -532},
    {98, -532},
    {93, -532},
    {88, -532},
    {83, -532},
    {78, -532},
    {73, -532},
    {68, -532},
    {63, -532},
    {-376, -1011},
    {-371, -1016},
    {-366, -1016},
    {-361, -1011},
    {-356, -1011},
    {-352, -1011},
    {-347, -1011},
    {-342, -1011},
    {-337, -1006},
    {-332, -1001},
    {-327, -1001},
    {-322, -996},
    {-317, -991},
    {-312, -986},
    {-308, -981},
    {-303, -977},
    {-298, -972},
    {-298, -967},
    {-293, -962},
    {-293, -957},
    {-288, -952},
    {-288, -947},
    {-288, -942},
    {-288, -938},
    {-288, -933},
    {-288, -928},
    {-283, -923},
    {-283, -918},
    {-288, -913},
    {-288, -908},
    {-288, -903},
    {-288, -898},
    {-283, -894},
    {-283, -889},
    {-288, -884},
    {-283, -879},
    {-283, -874},
    {-283, -869},
    {-283, -864},
    {-283, -859},
    {-283, -854},
    {-283, -850},
    {-283, -845},
    {-278, -840},
    {-273, -840},
    {-269, -840},
    {-264, -840},
    {-259, -840},
    {-254, -840},
    {-249, -840},
    {-244, -840},
    {-239, -840},
    {-234, -840},
    {-229, -840},
    {-225, -840},
    {-220, -840},
    {-215, -840},
    {-210, -840},
    {-205, -840},
    {-200, -840},
    {-195, -840},
    {-190, -840},
    {-186, -840},
    {-181, -840},
    {-176, -840},
    {-171, -840},
    {-166, -840},
    {-161, -840},
    {-156, -840},
    {-151, -840},
    {-146, -840},
    {-142, -840},
    {-137, -840},
    {-132, -840},
    {-127, -840},
    {-122, -840},
    {-117, -845},
    {-112, -845},
    {-112, -850},
    {-112, -854},
    {-112, -859},
    {-112, -864},
    {-112, -869},
    {-112, -874},
    {-112, -879},
    {-112, -884},
    {-112, -889},
    {-112, -894},
    {-112, -898},
    {-112, -903},
    {-112, -908},
    {-112, -913},
    {-112, -918},
    {-112, -923},
    {-112, -928},
    {-112, -933},
    {-112, -938},
    {-112, -942},
    {-112, -947},
    {-112, -952},
    {-107, -957},
    {-107, -962},
    {-103, -967},
    {-103, -972},
    {-98, -977},
    {-93, -981},
    {-88, -986},
    {-83, -991},
    {-78, -996},
    {-73, -1001},
    {-68, -1001},
    {-63, -1006},
    {-59, -1011},
    {-54, -1011},
    {-49, -1011},
    {-44, -1011},
    {-39, -1011},
    {-34, -1016},
    {-29, -1016},
    {-24, -1011},
    {-20, -1011},
    {-15, -1011},
    {-10, -1011},
    {-5, -1006},
    {0, -1006},
    {5, -1001},
    {10, -1001},
    {15, -996},
    {20, -991},
    {24, -986},
    {29, -981},
    {34, -977},
    {34, -972},
    {39, -967},
    {44, -962},
    {44, -957},
    {44, -952},
    {49, -947},
    {49, -942},
    {49, -938},
    {49, -933},
    {49, -928},
    {49, -923},
    {49, -918},
    {49, -913},
    {49, -908},
    {49, -903},
    {49, -898},
    {49, -894},
    {49, -889},
    {49, -884},
    {49, -879},
    {49, -874},
    {49, -869},
    {49, -864},
    {49, -859},
    {49, -854},
    {49, -850},
    {49, -845},
    {54, -845},
    {59, -840},
    {63, -840},
    {68, -840},
    {73, -840},
    {78, -840},
    {83, -840},
    {88, -840},
    {93, -840},
    {98, -840},
    {103, -840},
    {107, -840},
    {112, -840},
    {117, -840},
    {122, -840},
    {127, -840},
    {132, -840},
    {137, -840},
    {142, -840},
    {146, -840},
    {151, -840},
    {156, -840},
    {161, -840},
    {166, -840},
    {171, -840},
    {176, -840},
    {181, -840},
    {186, -840},
    {190, -840},
    {195, -840},
    {200, -840},
    {205, -840},
    {210, -840},
    {215, -840},
    {220, -840},
    {220, -845},
    {220, -850},
    {220, -854},
    {220, -859},
    {220, -864},
    {220, -869},
    {220, -874},
    {220, -879},
    {220, -884},
    {220, -889},
    {220, -894},
    {220, -898},
    {220, -903},
    {220, -908},
    {220, -913},
    {220, -918},
    {220, -923},
    {220, -928},
    {220, -933},
    {220, -938},
    {225, -942},
    {225, -947},
    {225, -952},
    {225, -957},
    {229, -962},
    {229, -967},
    {234, -972},
    {239, -977},
    {239, -981},
    {244, -986},
    {249, -991},
    {254, -996},
    {259, -996},
    {264, -1001},
    {269, -1006},
    {273, -1006},
    {278, -1011},
    {283, -1011},
    {288, -1011},
    {293, -1011},
    {298, -1011},
    {303, -1011},
    {308, -1011},
    {312, -1011},
    {317, -1011},
    {322, -1011},
    {327, -1011},
    {332, -1006},
    {337, -1006},
    {342, -1001},
    {347, -996},
    {352, -996},
    {356, -991},
    {361, -986},
    {366, -981},
    {371, -977},
    {371, -972},
    {376, -967},
    {381, -962},
    {381, -957},
    {381, -952},
    {381, -947},
    {386, -942},
    {386, -938},
    {386, -933},
    {386, -928},
    {386, -923},
    {386, -918},
    {386, -913},
    {386, -908},
    {386, -903},
    {386, -898},
    {386, -894},
    {386, -889},
    {386, -884},
    {386, -879},
    {386, -874},
    {386, -869},
    {386, -864},
    {386, -859},
    {386, -854},
    {386, -850},
    {386, -845},
    {391, -845},
    {396, -845},
    {400, -845},
    {405, -845},
    {410, -845},
    {415, -845},
    {420, -845},
    {425, -845},
    {430, -845},
    {435, -845},
    {439, -845},
    {444, -845},
    {449, -845},
    {454, -845},
    {459, -845},
    {464, -845},
    {469, -845},
    {474, -845},
    {479, -845},
    {483, -845},
    {488, -845},
    {493, -845},
    {498, -845},
    {503, -840},
    {508, -840},
    {513, -840},
    {518, -840},
    {522, -835},
    {527, -835},
    {532, -830},
    {537, -830},
    {542, -825},
    {547, -825},
    {552, -820},
    {557, -815},
    {562, -811},
    {566, -806},
    {571, -801},
    {576, -796},
    {576, -791},
    {581, -786},
    {586, -781},
    {586, -776},
    {591, -771},
    {591, -767},
    {591, -762},
    {591, -757},
    {596, -752},
    {596, -747},
    {596, -742},
    {596, -737},
    {596, -732},
    {596, -728},
    {596, -723},
    {596, -718},
    {596, -713},
    {596, -708},
    {596, -703},
    {596, -698},
    {596, -693},
    {596, -688},
    {596, -684},
    {596, -679},
    {596, -674},
    {596, -669},
    {596, -664},
    {596, -659},
    {596, -654},
    {596, -649},
    {596, -645},
    {601, -645},
    {605, -645},
    {610, -645},
    {615, -645},
    {620, -645},
    {625, -645},
    {630, -645},
    {635, -645},
    {640, -645},
    {645, -645},
    {649, -645},
    {654, -645},
    {659, -645},
    {664, -645},
    {669, -645},
    {674, -645},
    {679, -645},
    {684, -645},
    {688, -645},
    {693, -645},
    {698, -645},
    {703, -640},
    {708, -640},
    {713, -640},
    {718, -640},
    {723, -640},
    {728, -635},
    {732, -630},
    {737, -630},
    {742, -625},
    {747, -620},
    {752, -615},
    {757, -610},
    {762, -605},
    {767, -601},
    {771, -596},
    {771, -591},
    {776, -586},
    {776, -581},
    {776, -576},
    {776, -571},
    {781, -566},
    {781, -562},
    {781, -557},
    {781, -552},
    {781, -547},
    {776, -542},
    {776, -537},
    {776, -532},
    {776, -527},
    {771, -522},
    {767, -518},
    {767, -513},
    {762, -508},
    {757, -503},
    {752, -498},
    {747, -493},
    {742, -488},
    {737, -488},
    {732, -483},
    {728, -479},
    {723, -479},
    {718, -479},
    {713, -479},
    {708, -474},
    {703, -474},
    {698, -474},
    {693, -474},
    {688, -474},
    {684, -474},
    {679, -474},
    {674, -474},
    {669, -474},
    {664, -474},
    {659, -474},
    {654, -474},
    {649, -474},
    {645, -474},
    {640, -474},
    {635, -474},
    {630, -474},
    {625, -474},
    {620, -474},
    {615, -474},
    {610, -474},
    {605, -474},
    {601, -474},
    {596, -474},
    {596, -469},
    {596, -464},
    {596, -459},
    {596, -454},
    {596, -449},
    {596, -444},
    {596, -439},
    {596, -435},
    {596, -430},
    {596, -425},
    {596, -420},
    {596, -415},
    {596, -410},
    {596, -405},
    {596, -400},
    {596, -396},
    {596, -391},
    {596, -386},
    {596, -381},
    {596, -376},
    {596, -371},
    {596, -366},
    {596, -361},
    {596, -356},
    {596, -352},
    {596, -347},
    {596, -342},
    {596, -337},
    {596, -332},
    {596, -327},
    {596, -322},
    {596, -317},
    {596, -312},
    {601, -312},
    {605, -312},
    {610, -312},
    {615, -312},
    {620, -312},
    {625, -312},
    {630, -312},
    {635, -312},
    {640, -312},
    {645, -312},
    {649, -312},
    {654, -312},
    {659, -312},
    {664, -312},
    {669, -312},
    {674, -312},
    {679, -312},
    {684, -312},
    {688, -312},
    {693, -312},
    {698, -312},
    {703, -312},
    {708, -312},
    {713, -312},
    {718, -308},
    {723, -308},
    {728, -308},
    {732, -303},
    {737, -303},
    {742, -298},
    {747, -293},
    {752, -288},
    {757, -283},
    {762, -278},
    {767, -273},
    {767, -269},
    {771, -264},
    {776, -259},
    {776, -254},
    {776, -249},
    {776, -244},
    {781, -239},
    {781, -234},
    {781, -229},
    {781, -225},
    {781, -220},
    {776, -215},
    {776, -210},
    {776, -205},
    {776, -200},
    {771, -195},
    {767, -190},
    {767, -186},
    {762, -181},
    {757, -176},
    {752, -171},
    {747, -166},
    {742, -161},
    {737, -161},
    {732, -156},
    {728, -151},
    {723, -151},
    {718, -151},
    {713, -146},
    {708, -146},
    {703, -146},
    {698, -146},
    {693, -146},
    {688, -146},
    {684, -146},
    {679, -146},
    {674, -146},
    {669, -146},
    {664, -146},
    {659, -146},
    {654, -146},
    {649, -146},
    {645, -146},
    {640, -146},
    {635, -146},
    {630, -146},
    {625, -146},
    {620, -146},
    {615, -146},
    {610, -146},
    {605, -146},
    {601, -146},
    {596, -146},
    {596, -142},
    {596, -137},
    {596, -132},
    {596, -127},
    {596, -122},
    {596, -117},
    {596, -112},
    {596, -107},
    {596, -103},
    {596, -98},
    {596, -93},
    {596, -88},
    {596, -83},
    {596, -78},
    {596, -73},
    {596, -68},
    {596, -63},
    {596, -59},
    {596, -54},
    {596, -49},
    {596, -44},
    {596, -39},
    {596, -34},
    {596, -29},
    {596, -24},
    {596, -20},
    {596, -15},
    {596, -10},
    {596, -5},
    {596, 0},
    {596, 5},
    {596, 10},
    {596, 15},
    {601, 15},
    {605, 15},
    {610, 15},
    {615, 15},
    {620, 15},
    {625, 15},
    {630, 15},
    {635, 15},
    {640, 15},
    {645, 15},
    {649, 15},
    {654, 15},
    {659, 15},
    {664, 15},
    {669, 15},
    {674, 15},
    {679, 15},
    {684, 15},
    {688, 15},
    {693, 15},
    {698, 15},
    {703, 15},
    {708, 15},
    {713, 15},
    {718, 20},
    {723, 20},
    {728, 20},
    {732, 24},
    {737, 29},
    {742, 29},
    {747, 34},
    {752, 39},
    {757, 44},
    {762, 49},
    {767, 54},
    {767, 59},
    {771, 63},
    {776, 68},
    {776, 73},
    {776, 78},
    {776, 83},
    {781, 88},
    {781, 93},
    {781, 98},
    {781, 103},
    {781, 107},
    {776, 112},
    {776, 117},
    {776, 122},
    {776, 127},
    {771, 132},
    {771, 137},
    {767, 142},
    {762, 146},
    {757, 151},
    {752, 156},
    {747, 161},
    {742, 166},
    {737, 171},
    {732, 171},
    {728, 176},
    {723, 176},
    {718, 181},
    {713, 181},
    {708, 181},
    {703, 181},
    {698, 181},
    {693, 181},
    {688, 181},
    {684, 181},
    {679, 181},
    {674, 181},
    {669, 181},
    {664, 181},
    {659, 181},
    {654, 181},
    {649, 181},
    {645, 181},
    {640, 181},
    {635, 181},
    {630, 181},
    {625, 181},
    {620, 181},
    {615, 181},
    {610, 181},
    {605, 181},
    {601, 181},
    {596, 181},
    {596, 186},
    {596, 190},
    {596, 195},
    {596, 200},
    {596, 205},
    {596, 210},
    {596, 215},
    {596, 220},
    {596, 225},
    {596, 229},
    {596, 234},
    {596, 239},
    {596, 244},
    {596, 249},
    {596, 254},
    {596, 259},
    {596, 264},
    {596, 269},
    {596, 273},
    {596, 278},
    {596, 283},
    {596, 288},
    {596, 293},
    {591, 298},
    {591, 303},
    {591, 308},
    {591, 312},
    {586, 317},
    {586, 322},
    {581, 327},
    {581, 332},
    {576, 337},
    {571, 342},
    {566, 347},
    {562, 352},
    {557, 356},
    {552, 361},
    {547, 366},
    {542, 371},
    {537, 371},
    {532, 376},
    {527, 376},
    {522, 381},
    {518, 381},
    {513, 381},
    {508, 386},
    {503, 386},
    {498, 386},
    {493, 386},
    {488, 386},
    {483, 386},
    {479, 386},
    {474, 386},
    {469, 386},
    {464, 386},
    {459, 386},
    {454, 386},
    {449, 386},
    {444, 386},
    {439, 386},
    {435, 386},
    {430, 386},
    {425, 386},
    {420, 386},
    {415, 386},
    {410, 386},
    {405, 386},
    {400, 386},
    {396, 386},
    {391, 386},
    {386, 386},
    {386, 391},
    {386, 396},
    {386, 400},
    {386, 405},
    {386, 410},
    {386, 415},
    {386, 420},
    {386, 425},
    {386, 430},
    {386, 435},
    {386, 439},
    {386, 444},
    {386, 449},
    {386, 454},
    {386, 459},
    {386, 464},
    {386, 469},
    {386, 474},
    {386, 479},
    {386, 483},
    {386, 488},
    {381, 493},
    {381, 498},
    {381, 503},
    {381, 508},
    {376, 513},
    {371, 518},
    {371, 522},
    {366, 527},
    {361, 532},
    {356, 537},
    {352, 542},
    {347, 547},
    {342, 547},
    {337, 552},
    {332, 557},
    {327, 557},
    {322, 557},
    {317, 557},
    {312, 562},
    {308, 562},
    {303, 562},
    {298, 562},
    {293, 562},
    {288, 557},
    {283, 557},
    {278, 557},
    {273, 557},
    {269, 552},
    {264, 547},
    {259, 547},
    {254, 542},
    {249, 537},
    {244, 532},
    {239, 527},
    {234, 522},
    {234, 518},
    {229, 513},
    {229, 508},
    {225, 503},
    {225, 498},
    {225, 493},
    {225, 488},
    {220, 483},
    {220, 479},
    {220, 474},
    {220, 469},
    {220, 464},
    {220, 459},
    {220, 454},
    {220, 449},
    {220, 444},
    {220, 439},
    {220, 435},
    {220, 430},
    {220, 425},
    {220, 420},
    {220, 415},
    {220, 410},
    {220, 405},
    {220, 400},
    {220, 396},
    {220, 391},
    {220, 386},
    {215, 386},
    {210, 386},
    {205, 386},
    {200, 386},
    {195, 386},
    {190, 386},
    {186, 386},
    {181, 386},
    {176, 386},
    {171, 386},
    {166, 386},
    {161, 386},
    {156, 386},
    {151, 386},
    {146, 386},
    {142, 386},
    {137, 386},
    {132, 386},
    {127, 386},
    {122, 386},
    {117, 386},
    {112, 386},
    {107, 386},
    {103, 386},
    {98, 386},
    {93, 386},
    {88, 386},
    {83, 386},
    {78, 386},
    {73, 386},
    {68, 386},
    {63, 386},
    {59, 386},
    {54, 386},
    {49, 386},
    {49, 391},
    {49, 396},
    {49, 400},
    {49, 405},
    {49, 410},
    {49, 415},
    {49, 420},
    {49, 425},
    {49, 430},
    {49, 435},
    {49, 439},
    {49, 444},
    {49, 449},
    {49, 454},
    {49, 459},
    {49, 464},
    {49, 469},
    {49, 474},
    {49, 479},
    {49, 483},
    {49, 488},
    {44, 493},
    {44, 498},
    {44, 503},
    {44, 508},
    {39, 513},
    {34, 518},
    {34, 522},
    {29, 527},
    {24, 532},
    {20, 537},
    {15, 542},
    {10, 547},
    {5, 547},
    {0, 552},
    {-5, 557},
    {-10, 557},
    {-15, 557},
    {-20, 557},
    {-24, 562},
    {-29, 562},
    {-34, 562},
    {-39, 562},
    {-44, 562},
    {-49, 557},
    {-54, 557},
    {-59, 557},
    {-63, 552},
    {-68, 552},
    {-73, 547},
    {-78, 547},
    {-83, 542},
    {-88, 537},
    {-93, 532},
    {-98, 527},
    {-103, 522},
    {-103, 518},
    {-107, 513},
    {-107, 508},
    {-112, 503},
    {-112, 498},
    {-112, 493},
    {-112, 488},
    {-117, 483},
    {-117, 479},
    {-117, 474},
    {-117, 469},
    {-117, 464},
    {-117, 459},
    {-117, 454},
    {-117, 449},
    {-117, 444},
    {-117, 439},
    {-117, 435},
    {-117, 430},
    {-117, 425},
    {-117, 420},
    {-117, 415},
    {-117, 410},
    {-117, 405},
    {-117, 400},
    {-117, 396},
    {-117, 391},
    {-117, 386},
    {-122, 386},
    {-127, 386},
    {-132, 386},
    {-137, 386},
    {-142, 386},
    {-146, 386},
    {-151, 386},
    {-156, 386},
    {-161, 386},
    {-166, 386},
    {-171, 386},
    {-176, 386},
    {-181, 386},
    {-186, 386},
    {-190, 386},
    {-195, 386},
    {-200, 386},
    {-205, 386},
    {-210, 386},
    {-215, 386},
    {-220, 386},
    {-225, 386},
    {-229, 386},
    {-234, 386},
    {-239, 386},
    {-244, 386},
    {-249, 386},
    {-254, 386},
    {-259, 386},
    {-264, 386},
    {-269, 386},
    {-273, 386},
    {-278, 386},
    {-283, 386},
    {-283, 391},
    {-288, 396},
    {-288, 400},
    {-288, 405},
    {-288, 410},
    {-288, 415},
    {-288, 420},
    {-288, 425},
    {-288, 430},
    {-288, 435},
    {-288, 439},
    {-288, 444},
    {-288, 449},
    {-288, 454},
    {-288, 459},
    {-288, 464},
    {-288, 469},
    {-288, 474},
    {-288, 479},
    {-288, 483},
    {-288, 488},
    {-288, 493},
    {-288, 498},
    {-288, 503},
    {-293, 508},
    {-293, 513},
    {-298, 518},
    {-303, 522},
    {-303, 527},
    {-308, 532},
    {-312, 537},
    {-317, 542},
    {-322, 542},
    {-327, 547},
    {-332, 552},
    {-337, 552},
    {-342, 557},
    {-347, 557},
    {-352, 557},
    {-356, 557},
    {-361, 562},
    {-366, 562},
    {-371, 562},
    {-376, 562},
    {-381, 557},
    {-386, 557},
    {-391, 557},
    {-396, 557},
    {-400, 552},
    {-405, 552},
    {-410, 547},
    {-415, 542},
    {-420, 537},
    {-425, 532},
    {-430, 527},
    {-435, 522},
    {-439, 518},
    {-444, 513},
    {-444, 508},
    {-444, 503},
    {-449, 498},
    {-449, 493},
    {-449, 488},
    {-449, 483},
    {-449, 479},
    {-449, 474},
    {-449, 469},
    {-449, 464},
    {-449, 459},
    {-449, 454},
    {-449, 449},
    {-449, 444},
    {-449, 439},
    {-449, 435},
    {-449, 430},
    {-449, 425},
    {-449, 420},
    {-449, 415},
    {-449, 410},
    {-449, 405},
    {-449, 400},
    {-449, 396},
    {-449, 391},
    {-454, 386},
    {-459, 386},
    {-464, 386},
    {-469, 386},
    {-474, 386},
    {-479, 386},
    {-483, 386},
    {-488, 386},
    {-493, 386},
    {-498, 386},
    {-503, 386},
    {-508, 386},
    {-513, 386},
    {-518, 386},
    {-522, 386},
    {-527, 386},
    {-532, 386},
    {-537, 386},
    {-542, 386},
    {-547, 386},
    {-552, 386},
    {-557, 386},
    {-562, 386},
    {-566, 386},
    {-571, 386},
    {-576, 386},
    {-581, 381},
    {-586, 381},
    {-591, 381},
    {-596, 381},
    {-601, 376},
    {-605, 371},
    {-610, 371},
    {-615, 366},
    {-620, 366},
    {-625, 361},
    {-630, 356},
    {-635, 352},
    {-640, 347},
    {-645, 342},
    {-645, 337},
    {-649, 332},
    {-649, 327},
    {-654, 322},
    {-659, 317},
    {-659, 312},
    {-659, 308},
    {-659, 303},
    {-664, 298},
    {-664, 293},
    {-664, 288},
    {-664, 283},
    {-664, 278},
    {-664, 273},
    {-664, 269},
    {-664, 264},
    {-664, 259},
    {-664, 254},
    {-664, 249},
    {-664, 244},
    {-664, 239},
    {-664, 234},
    {-664, 229},
    {-664, 225},
    {-664, 220},
    {-664, 215},
    {-664, 210},
    {-664, 205},
    {-664, 200},
    {-664, 195},
    {-664, 190},
    {-664, 186},
    {-669, 181},
    {-674, 181},
    {-679, 181},
    {-684, 181},
    {-688, 181},
    {-693, 181},
    {-698, 181},
    {-703, 181},
    {-708, 181},
    {-713, 181},
    {-718, 181},
    {-723, 181},
    {-728, 181},
    {-732, 181},
    {-737, 181},
    {-742, 181},
    {-747, 181},
    {-752, 181},
    {-757, 181},
    {-762, 181},
    {-767, 181},
    {-771, 181},
    {-776, 181},
    {-781, 181},
    {-786, 181},
    {-791, 176},
    {-796, 176},
    {-801, 176},
    {-806, 171},
    {-811, 166},
    {-815, 166},
    {-820, 161},
    {-825, 156},
    {-830, 151},
    {-835, 146},
    {-840, 142},
    {-840, 137},
    {-845, 132},
    {-845, 127},
    {-850, 122},
    {-850, 117},
    {-850, 112},
    {-850, 107},
    {-854, 103},
    {-854, 98},
    {-854, 93},
    {-850, 88},
    {-850, 83},
    {-850, 78},
    {-850, 73},
    {-845, 68},
    {-845, 63},
    {-840, 59},
    {-840, 54},
    {-835, 49},
    {-830, 44},
    {-825, 39},
    {-820, 34},
    {-815, 29},
    {-811, 29},
    {-806, 24},
    {-801, 20},
    {-796, 20},
    {-791, 20},
    {-786, 15},
    {-781, 15},
    {-776, 15},
    {-771, 15},
    {-767, 15},
    {-762, 15},
    {-757, 15},
    {-752, 15},
    {-747, 15},
    {-742, 15},
    {-737, 15},
    {-732, 15},
    {-728, 15},
    {-723, 15},
    {-718, 15},
    {-713, 15},
    {-708, 15},
    {-703, 15},
    {-698, 15},
    {-693, 15},
    {-688, 15},
    {-684, 15},
    {-679, 15},
    {-674, 15},
    {-669, 15},
    {-664, 10},
    {-664, 5},
    {-664, 0},
    {-664, -5},
    {-664, -10},
    {-664, -15},
    {-664, -20},
    {-664, -24},
    {-664, -29},
    {-664, -34},
    {-664, -39},
    {-664, -44},
    {-664, -49},
    {-664, -54},
    {-664, -59},
    {-664, -63},
    {-664, -68},
    {-664, -73},
    {-664, -78},
    {-664, -83},
    {-664, -88},
    {-664, -93},
    {-664, -98},
    {-664, -103},
    {-664, -107},
    {-664, -112},
    {-664, -117},
    {-664, -122},
    {-664, -127},
    {-664, -132},
    {-664, -137},
    {-664, -142},
    {-664, -146},
    {-669, -146},
    {-674, -146},
    {-679, -146},
    {-684, -146},
    {-688, -146},
    {-693, -146},
    {-698, -146},
    {-703, -146},
    {-708, -146},
    {-713, -146},
    {-718, -146},
    {-723, -146},
    {-728, -146},
    {-732, -146},
    {-737, -146},
    {-742, -146},
    {-747, -146},
    {-752, -146},
    {-757, -146},
    {-762, -146},
    {-767, -146},
    {-771, -146},
    {-776, -146},
    {-781, -146},
    {-786, -146},
    {-791, -151},
    {-796, -151},
    {-801, -151},
    {-806, -156},
    {-811, -161},
    {-815, -161},
    {-820, -166},
    {-825, -171},
    {-830, -176},
    {-835, -181},
    {-840, -186},
    {-840, -190},
    {-845, -195},
    {-845, -200},
    {-850, -205},
    {-850, -210},
    {-850, -215},
    {-850, -220},
    {-854, -225},
    {-854, -229},
    {-854, -234},
    {-850, -239},
    {-850, -244},
    {-850, -249},
    {-850, -254},
    {-850, -259},
    {-845, -264},
    {-840, -269},
    {-840, -273},
    {-835, -278},
    {-830, -283},
    {-825, -288},
    {-820, -293},
    {-815, -298},
    {-811, -303},
    {-806, -303},
    {-801, -308},
    {-796, -308},
    {-791, -308},
    {-786, -312},
    {-781, -312},
    {-776, -312},
    {-771, -312},
    {-767, -312},
    {-762, -312},
    {-757, -312},
    {-752, -312},
    {-747, -312},
    {-742, -312},
    {-737, -312},
    {-732, -312},
    {-728, -312},
    {-723, -312},
    {-718, -312},
    {-713, -312},
    {-708, -312},
    {-703, -312},
    {-698, -312},
    {-693, -312},
    {-688, -312},
    {-684, -312},
    {-679, -312},
    {-674, -312},
    {-669, -312},
    {-664, -317},
    {-664, -322},
    {-664, -327},
    {-664, -332},
    {-664, -337},
    {-664, -342},
    {-664, -347},
    {-664, -352},
    {-664, -356},
    {-664, -361},
    {-664, -366},
    {-664, -371},
    {-664, -376},
    {-664, -381},
    {-664, -386},
    {-664, -391},
    {-664, -396},
    {-664, -400},
    {-664, -405},
    {-664, -410},
    {-664, -415},
    {-664, -420},
    {-664, -425},
    {-664, -430},
    {-664, -435},
    {-664, -439},
    {-664, -444},
    {-664, -449},
    {-664, -454},
    {-664, -459},
    {-664, -464},
    {-664, -469},
    {-664, -474},
    {-669, -474},
    {-674, -474},
    {-679, -474},
    {-684, -474},
    {-688, -474},
    {-693, -474},
    {-698, -474},
    {-703, -474},
    {-708, -474},
    {-713, -474},
    {-718, -474},
    {-723, -474},
    {-728, -474},
    {-732, -474},
    {-737, -474},
    {-742, -474},
    {-747, -474},
    {-752, -474},
    {-757, -474},
    {-762, -474},
    {-767, -474},
    {-771, -474},
    {-776, -474},
    {-781, -479},
    {-786, -479},
    {-791, -479},
    {-796, -479},
    {-801, -483},
    {-806, -483},
    {-811, -488},
    {-815, -493},
    {-820, -493},
    {-825, -498},
    {-830, -503},
    {-830, -508},
    {-835, -513},
    {-840, -518},
    {-845, -522},
    {-845, -527},
    {-850, -532},
    {-850, -537},
    {-850, -542},
    {-850, -547},
    {-850, -552},
    {-854, -557},
    {-854, -562},
    {-850, -566},
    {-850, -571},
    {-850, -576},
    {-850, -581},
    {-850, -586},
    {-845, -591},
    {-840, -596},
    {-840, -601},
    {-835, -605},
    {-830, -610},
    {-825, -615},
    {-820, -620},
    {-815, -625},
    {-811, -630},
    {-806, -630},
    {-801, -635},
    {-796, -640},
    {-791, -640},
    {-786, -640},
    {-781, -640},
    {-776, -640},
    {-771, -645},
    {-767, -645},
    {-762, -645},
    {-757, -645},
    {-752, -645},
    {-747, -645},
    {-742, -645},
    {-737, -645},
    {-732, -645},
    {-728, -645},
    {-723, -645},
    {-718, -645},
    {-713, -645},
    {-708, -645},
    {-703, -645},
    {-698, -645},
    {-693, -645},
    {-688, -645},
    {-684, -645},
    {-679, -645},
    {-674, -645},
    {-669, -645},
    {-664, -645},
    {-664, -649},
    {-664, -654},
    {-664, -659},
    {-664, -664},
    {-664, -669},
    {-664, -674},
    {-664, -679},
    {-664, -684},
    {-664, -688},
    {-664, -693},
    {-664, -698},
    {-664, -703},
    {-664, -708},
    {-664, -713},
    {-664, -718},
    {-664, -723},
    {-664, -728},
    {-664, -732},
    {-664, -737},
    {-664, -742},
    {-664, -747},
    {-664, -752},
    {-664, -757},
    {-659, -762},
    {-659, -767},
    {-659, -771},
    {-654, -776},
    {-654, -781},
    {-649, -786},
    {-649, -791},
    {-645, -796},
    {-640, -801},
    {-635, -806},
    {-630, -811},
    {-625, -815},
    {-620, -820},
    {-615, -825},
    {-610, -830},
    {-605, -830},
    {-601, -835},
    {-596, -835},
    {-591, -840},
    {-586, -840},
    {-581, -840},
    {-576, -845},
    {-571, -845},
    {-566, -845},
    {-562, -845},
    {-557, -845},
    {-552, -845},
    {-547, -845},
    {-542, -845},
    {-537, -845},
    {-532, -845},
    {-527, -845},
    {-522, -845},
    {-518, -845},
    {-513, -845},
    {-508, -845},
    {-503, -845},
    {-498, -845},
    {-493, -845},
    {-488, -845},
    {-483, -845},
    {-479, -845},
    {-474, -845},
    {-469, -845},
    {-464, -845},
    {-459, -845},
    {-454, -845},
    {-449, -850},
    {-449, -854},
    {-449, -859},
    {-449, -864},
    {-449, -869},
    {-449, -874},
    {-449, -879},
    {-449, -884},
    {-449, -889},
    {-449, -894},
    {-449, -898},
    {-449, -903},
    {-449, -908},
    {-449, -913},
    {-449, -918},
    {-449, -923},
    {-449, -928},
    {-449, -933},
    {-449, -938},
    {-449, -942},
    {-449, -947},
    {-444, -952},
    {-444, -957},
    {-444, -962},
    {-439, -967},
    {-439, -972},
    {-435, -977},
    {-430, -981},
    {-425, -986},
    {-420, -991},
    {-415, -996},
    {-410, -1001},
    {-405, -1001},
    {-400, -1006},
    {-396, -1006},
    {-391, -1011},
    {-386, -1011},
    {-381, -1011},
    {-371, -1016},
    {-376, -1011},
    {-381, -1011},
    {-386, -1011},
    {-391, -1011},
    {-396, -1006},
    {-400, -1006},
    {-405, -1006},
    {-410, -1001},
    {-415, -1001},
    {-420, -996},
    {-425, -991},
    {-430, -986},
    {-435, -981},
    {-439, -977},
    {-439, -972},
    {-439, -967},
    {-444, -962},
    {-444, -957},
    {-444, -952},
    {-449, -947},
    {-449, -942},
    {-449, -938},
    {-449, -933},
    {-449, -928},
    {-449, -923},
    {-449, -918},
    {-449, -913},
    {-449, -908},
    {-449, -903},
    {-449, -898},
    {-449, -894},
    {-449, -889},
    {-449, -884},
    {-449, -879},
    {-449, -874},
    {-449, -869},
    {-449, -864},
    {-449, -859},
    {-449, -854},
    {-454, -850},
    {-459, -845},
    {-464, -845},
    {-469, -845},
    {-474, -845},
    {-479, -845},
    {-483, -845},
    {-488, -845},
    {-493, -845},
    {-498, -845},
    {-503, -845},
    {-508, -845},
    {-513, -845},
    {-518, -845},
    {-522, -845},
    {-527, -845},
    {-532, -845},
    {-537, -845},
    {-542, -845},
    {-547, -845},
    {-552, -845},
    {-557, -845},
    {-562, -845},
    {-566, -845},
    {-571, -845},
    {-576, -845},
    {-581, -840},
    {-586, -840},
    {-591, -840},
    {-596, -835},
    {-601, -835},
    {-605, -835},
    {-610, -830},
    {-615, -830},
    {-620, -825},
    {-625, -820},
    {-630, -815},
    {-635, -811},
    {-640, -806},
    {-645, -801},
    {-649, -796},
    {-649, -791},
    {-654, -786},
    {-654, -781},
    {-654, -776},
    {-659, -771},
    {-659, -767},
    {-659, -762},
    {-664, -757},
    {-664, -752},
    {-664, -747},
    {-664, -742},
    {-664, -737},
    {-664, -732},
    {-664, -728},
    {-664, -723},
    {-664, -718},
    {-664, -713},
    {-664, -708},
    {-664, -703},
    {-664, -698},
    {-664, -693},
    {-664, -688},
    {-664, -684},
    {-664, -679},
    {-664, -674},
    {-664, -669},
    {-664, -664},
    {-664, -659},
    {-664, -654},
    {-664, -649},
    {-669, -645},
    {-674, -645},
    {-679, -645},
    {-684, -645},
    {-688, -645},
    {-693, -645},
    {-698, -645},
    {-703, -645},
    {-708, -645},
    {-713, -645},
    {-718, -645},
    {-723, -645},
    {-728, -645},
    {-732, -645},
    {-737, -645},
    {-742, -645},
    {-747, -645},
    {-752, -645},
    {-757, -645},
    {-762, -645},
    {-767, -645},
    {-771, -645},
    {-776, -640},
    {-781, -640},
    {-786, -640},
    {-791, -640},
    {-796, -640},
    {-801, -635},
    {-806, -635},
    {-811, -630},
    {-815, -630},
    {-820, -625},
    {-825, -620},
    {-830, -615},
    {-835, -610},
    {-840, -605},
    {-840, -601},
    {-845, -596},
    {-845, -591},
    {-850, -586},
    {-850, -581},
    {-850, -576},
    {-850, -571},
    {-850, -566},
    {-854, -562},
    {-854, -557},
    {-850, -552},
    {-850, -547},
    {-850, -542},
    {-850, -537},
    {-850, -532},
    {-845, -527},
    {-845, -522},
    {-845, -518},
    {-840, -513},
    {-835, -508},
    {-830, -503},
    {-830, -498},
    {-825, -493},
    {-820, -493},
    {-815, -488},
    {-811, -483},
    {-806, -483},
    {-801, -483},
    {-796, -479},
    {-791, -479},
    {-786, -479},
    {-781, -479},
    {-776, -474},
    {-771, -474},
    {-767, -474},
    {-762, -474},
    {-757, -474},
    {-752, -474},
    {-747, -474},
    {-742, -474},
    {-737, -474},
    {-732, -474},
    {-728, -474},
    {-723, -474},
    {-718, -474},
    {-713, -474},
    {-708, -474},
    {-703, -474},
    {-698, -474},
    {-693, -474},
    {-688, -474},
    {-684, -474},
    {-679, -474},
    {-674, -474},
    {-669, -474},
    {-664, -469},
    {-664, -464},
    {-664, -459},
    {-664, -454},
    {-664, -449},
    {-664, -444},
    {-664, -439},
    {-664, -435},
    {-664, -430},
    {-664, -425},
    {-664, -420},
    {-664, -415},
    {-664, -410},
    {-664, -405},
    {-664, -400},
    {-664, -396},
    {-664, -391},
    {-664, -386},
    {-664, -381},
    {-664, -376},
    {-664, -371},
    {-664, -366},
    {-664, -361},
    {-664, -356},
    {-664, -352},
    {-664, -347},
    {-664, -342},
    {-664, -337},
    {-664, -332},
    {-664, -327},
    {-664, -322},
    {-669, -317},
    {-674, -312},
    {-679, -312},
    {-684, -312},
    {-688, -312},
    {-693, -312},
    {-698, -312},
    {-703, -312},
    {-708, -312},
    {-713, -312},
    {-718, -312},
    {-723, -312},
    {-728, -312},
    {-732, -312},
    {-737, -312},
    {-742, -312},
    {-747, -312},
    {-752, -312},
    {-757, -312},
    {-762, -312},
    {-767, -312},
    {-771, -312},
    {-776, -312},
    {-781, -312},
    {-786, -312},
    {-791, -308},
    {-796, -308},
    {-801, -308},
    {-806, -303},
    {-811, -303},
    {-815, -303},
    {-820, -298},
    {-825, -293},
    {-830, -288},
    {-835, -283},
    {-840, -278},
    {-840, -273},
    {-845, -269},
    {-845, -264},
    {-850, -259},
    {-850, -254},
    {-850, -249},
    {-850, -244},
    {-850, -239},
    {-854, -234},
    {-854, -229},
    {-854, -225},
    {-850, -220},
    {-850, -215},
    {-850, -210},
    {-850, -205},
    {-845, -200},
    {-845, -195},
    {-845, -190},
    {-840, -186},
    {-840, -181},
    {-835, -176},
    {-830, -171},
    {-825, -166},
    {-820, -161},
    {-815, -161},
    {-811, -156},
    {-806, -156},
    {-801, -151},
    {-796, -151},
    {-791, -151},
    {-786, -146},
    {-781, -146},
    {-776, -146},
    {-771, -146},
    {-767, -146},
    {-762, -146},
    {-757, -146},
    {-752, -146},
    {-747, -146},
    {-742, -146},
    {-737, -146},
    {-732, -146},
    {-728, -146},
    {-723, -146},
    {-718, -146},
    {-713, -146},
    {-708, -146},
    {-703, -146},
    {-698, -146},
    {-693, -146},
    {-688, -146},
    {-684, -146},
    {-679, -146},
    {-674, -146},
    {-669, -146},
    {-664, -142},
    {-664, -137},
    {-664, -132},
    {-664, -127},
    {-664, -122},
    {-664, -117},
    {-664, -112},
    {-664, -107},
    {-664, -103},
    {-664, -98},
    {-664, -93},
    {-664, -88},
    {-664, -83},
    {-664, -78},
    {-664, -73},
    {-664, -68},
    {-664, -63},
    {-664, -59},
    {-664, -54},
    {-664, -49},
    {-664, -44},
    {-664, -39},
    {-664, -34},
    {-664, -29},
    {-664, -24},
    {-664, -20},
    {-664, -15},
    {-664, -10},
    {-664, -5},
    {-664, 0},
    {-664, 5},
    {-669, 10},
    {-674, 15},
    {-679, 15},
    {-684, 15},
    {-688, 15},
    {-693, 15},
    {-698, 15},
    {-703, 15},
    {-708, 15},
    {-713, 15},
    {-718, 15},
    {-723, 15},
    {-728, 15},
    {-732, 15},
    {-737, 15},
    {-742, 15},
    {-747, 15},
    {-752, 15},
    {-757, 15},
    {-762, 15},
    {-767, 15},
    {-771, 15},
    {-776, 15},
    {-781, 15},
    {-786, 15},
    {-791, 20},
    {-796, 20},
    {-801, 20},
    {-806, 24},
    {-811, 24},
    {-815, 29},
    {-820, 29},
    {-825, 34},
    {-830, 39},
    {-835, 44},
    {-840, 49},
    {-840, 54},
    {-845, 59},
    {-845, 63},
    {-845, 68},
    {-850, 73},
    {-850, 78},
    {-850, 83},
    {-850, 88},
    {-854, 93},
    {-854, 98},
    {-854, 103},
    {-850, 107},
    {-850, 112},
    {-850, 117},
    {-850, 122},
    {-845, 127},
    {-845, 132},
    {-845, 137},
    {-840, 142},
    {-840, 146},
    {-835, 151},
    {-830, 156},
    {-825, 161},
    {-820, 166},
    {-815, 166},
    {-811, 171},
    {-806, 176},
    {-801, 176},
    {-796, 176},
    {-791, 176},
    {-786, 181},
    {-781, 181},
    {-776, 181},
    {-771, 181},
    {-767, 181},
    {-762, 181},
    {-757, 181},
    {-752, 181},
    {-747, 181},
    {-742, 181},
    {-737, 181},
    {-732, 181},
    {-728, 181},
    {-723, 181},
    {-718, 181},
    {-713, 181},
    {-708, 181},
    {-703, 181},
    {-698, 181},
    {-693, 181},
    {-688, 181},
    {-684, 181},
    {-679, 181},
    {-674, 181},
    {-669, 186},
    {-664, 190},
    {-664, 195},
    {-664, 200},
    {-664, 205},
    {-664, 210},
    {-664, 215},
    {-664, 220},
    {-664, 225},
    {-664, 229},
    {-664, 234},
    {-664, 239},
    {-664, 244},
    {-664, 249},
    {-664, 254},
    {-664, 259},
    {-664, 264},
    {-664, 269},
    {-664, 273},
    {-664, 278},
    {-664, 283},
    {-664, 288},
    {-664, 293},
    {-664, 298},
    {-659, 303},
    {-659, 308},
    {-659, 312},
    {-659, 317},
    {-654, 322},
    {-654, 327},
    {-649, 332},
    {-649, 337},
    {-645, 342},
    {-645, 347},
    {-640, 352},
    {-635, 356},
    {-630, 361},
    {-625, 366},
    {-620, 366},
    {-615, 371},
    {-610, 371},
    {-605, 376},
    {-601, 376},
    {-596, 381},
    {-591, 381},
    {-586, 381},
    {-581, 381},
    {-576, 386},
    {-571, 386},
    {-566, 386},
    {-562, 386},
    {-557, 386},
    {-552, 386},
    {-547, 386},
    {-542, 386},
    {-537, 386},
    {-532, 386},
    {-527, 386},
    {-522, 386},
    {-518, 386},
    {-513, 386},
    {-508, 386},
    {-503, 386},
    {-498, 386},
    {-493, 386},
    {-488, 386},
    {-483, 386},
    {-479, 386},
    {-474, 386},
    {-469, 386},
    {-464, 386},
    {-459, 386},
    {-454, 391},
    {-449, 396},
    {-449, 400},
    {-449, 405},
    {-449, 410},
    {-449, 415},
    {-449, 420},
    {-449, 425},
    {-449, 430},
    {-449, 435},
    {-449, 439},
    {-449, 444},
    {-449, 449},
    {-449, 454},
    {-449, 459},
    {-449, 464},
    {-449, 469},
    {-449, 474},
    {-449, 479},
    {-449, 483},
    {-449, 488},
    {-449, 493},
    {-449, 498},
    {-444, 503},
    {-444, 508},
    {-444, 513},
    {-439, 518},
    {-439, 522},
    {-435, 527},
    {-430, 532},
    {-425, 537},
    {-420, 542},
    {-415, 547},
    {-410, 552},
    {-405, 552},
    {-400, 552},
    {-396, 557},
    {-391, 557},
    {-386, 557},
    {-381, 557},
    {-376, 562},
    {-371, 562},
    {-366, 562},
    {-361, 562},
    {-356, 557},
    {-352, 557},
    {-347, 557},
    {-342, 557},
    {-337, 552},
    {-332, 552},
    {-327, 552},
    {-322, 547},
    {-317, 542},
    {-312, 542},
    {-308, 537},
    {-303, 532},
    {-303, 527},
    {-298, 522},
    {-298, 518},
    {-293, 513},
    {-293, 508},
    {-288, 503},
    {-288, 498},
    {-288, 493},
    {-288, 488},
    {-288, 483},
    {-288, 479},
    {-288, 474},
    {-288, 469},
    {-288, 464},
    {-288, 459},
    {-288, 454},
    {-288, 449},
    {-288, 444},
    {-288, 439},
    {-288, 435},
    {-288, 430},
    {-288, 425},
    {-288, 420},
    {-288, 415},
    {-288, 410},
    {-288, 405},
    {-288, 400},
    {-288, 396},
    {-283, 391},
    {-278, 386},
    {-273, 386},
    {-269, 386},
    {-264, 386},
    {-259, 386},
    {-254, 386},
    {-249, 386},
    {-244, 386},
    {-239, 386},
    {-234, 386},
    {-229, 386},
    {-225, 386},
    {-220, 386},
    {-215, 386},
    {-210, 386},
    {-205, 386},
    {-200, 386},
    {-195, 386},
    {-190, 386},
    {-186, 386},
    {-181, 386},
    {-176, 386},
    {-171, 386},
    {-166, 386},
    {-161, 386},
    {-156, 386},
    {-151, 386},
    {-146, 386},
    {-142, 386},
    {-137, 386},
    {-132, 386},
    {-127, 386},
    {-122, 386},
    {-117, 391},
    {-117, 396},
    {-117, 400},
    {-117, 405},
    {-117, 410},
    {-117, 415},
    {-117, 420},
    {-117, 425},
    {-117, 430},
    {-117, 435},
    {-117, 439},
    {-117, 444},
    {-117, 449},
    {-117, 454},
    {-117, 459},
    {-117, 464},
    {-117, 469},
    {-117, 474},
    {-117, 479},
    {-117, 483},
    {-112, 488},
    {-112, 493},
    {-112, 498},
    {-112, 503},
    {-107, 508},
    {-107, 513},
    {-107, 518},
    {-103, 522},
    {-103, 527},
    {-98, 532},
    {-93, 537},
    {-88, 542},
    {-83, 547},
    {-78, 547},
    {-73, 552},
    {-68, 552},
    {-63, 552},
    {-59, 557},
    {-54, 557},
    {-49, 557},
    {-44, 562},
    {-39, 562},
    {-34, 562},
    {-29, 562},
    {-24, 562},
    {-20, 557},
    {-15, 557},
    {-10, 557},
    {-5, 557},
    {0, 552},
    {5, 552},
    {10, 547},
    {15, 547},
    {20, 542},
    {24, 537},
    {29, 532},
    {34, 527},
    {34, 522},
    {39, 518},
    {39, 513},
    {44, 508},
    {44, 503},
    {44, 498},
    {44, 493},
    {49, 488},
    {49, 483},
    {49, 479},
    {49, 474},
    {49, 469},
    {49, 464},
    {49, 459},
    {49, 454},
    {49, 449},
    {49, 444},
    {49, 439},
    {49, 435},
    {49, 430},
    {49, 425},
    {49, 420},
    {49, 415},
    {49, 410},
    {49, 405},
    {49, 400},
    {49, 396},
    {49, 391},
    {54, 386},
    {59, 386},
    {63, 386},
    {68, 386},
    {73, 386},
    {78, 386},
    {83, 386},
    {88, 386},
    {93, 386},
    {98, 386},
    {103, 386},
    {107, 386},
    {112, 386},
    {117, 386},
    {122, 386},
    {127, 386},
    {132, 386},
    {137, 386},
    {142, 386},
    {146, 386},
    {151, 386},
    {156, 386},
    {161, 386},
    {166, 386},
    {171, 386},
    {176, 386},
    {181, 386},
    {186, 386},
    {190, 386},
    {195, 386},
    {200, 386},
    {205, 386},
    {210, 386},
    {215, 386},
    {220, 391},
    {220, 396},
    {220, 400},
    {220, 405},
    {220, 410},
    {220, 415},
    {220, 420},
    {220, 425},
    {220, 430},
    {220, 435},
    {220, 439},
    {220, 444},
    {220, 449},
    {220, 454},
    {220, 459},
    {220, 464},
    {220, 469},
    {220, 474},
    {220, 479},
    {220, 483},
    {225, 488},
    {225, 493},
    {225, 498},
    {225, 503},
    {229, 508},
    {229, 513},
    {229, 518},
    {234, 522},
    {234, 527},
    {239, 532},
    {244, 537},
    {249, 542},
    {254, 547},
    {259, 547},
    {264, 552},
    {269, 552},
    {273, 557},
    {278, 557},
    {283, 557},
    {288, 557},
    {293, 562},
    {298, 562},
    {303, 562},
    {308, 562},
    {312, 562},
    {317, 557},
    {322, 557},
    {327, 557},
    {332, 557},
    {337, 552},
    {342, 552},
    {347, 547},
    {352, 547},
    {356, 542},
    {361, 537},
    {366, 532},
    {371, 527},
    {371, 522},
    {376, 518},
    {376, 513},
    {381, 508},
    {381, 503},
    {381, 498},
    {381, 493},
    {386, 488},
    {386, 483},
    {386, 479},
    {386, 474},
    {386, 469},
    {386, 464},
    {386, 459},
    {386, 454},
    {386, 449},
    {386, 444},
    {386, 439},
    {386, 435},
    {386, 430},
    {386, 425},
    {386, 420},
    {386, 415},
    {386, 410},
    {386, 405},
    {386, 400},
    {386, 396},
    {386, 391},
    {391, 386},
    {396, 386},
    {400, 386},
    {405, 386},
    {410, 386},
    {415, 386},
    {420, 386},
    {425, 386},
    {430, 386},
    {435, 386},
    {439, 386},
    {444, 386},
    {449, 386},
    {454, 386},
    {459, 386},
    {464, 386},
    {469, 386},
    {474, 386},
    {479, 386},
    {483, 386},
    {488, 386},
    {493, 386},
    {498, 386},
    {503, 386},
    {508, 386},
    {513, 381},
    {518, 381},
    {522, 381},
    {527, 376},
    {532, 376},
    {537, 376},
    {542, 371},
    {547, 371},
    {552, 366},
    {557, 361},
    {562, 356},
    {566, 352},
    {571, 347},
    {576, 342},
    {581, 337},
    {581, 332},
    {586, 327},
    {586, 322},
    {586, 317},
    {591, 312},
    {591, 308},
    {591, 303},
    {591, 298},
    {596, 293},
    {596, 288},
    {596, 283},
    {596, 278},
    {596, 273},
    {596, 269},
    {596, 264},
    {596, 259},
    {596, 254},
    {596, 249},
    {596, 244},
    {596, 239},
    {596, 234},
    {596, 229},
    {596, 225},
    {596, 220},
    {596, 215},
    {596, 210},
    {596, 205},
    {596, 200},
    {596, 195},
    {596, 190},
    {596, 186},
    {601, 181},
    {605, 181},
    {610, 181},
    {615, 181},
    {620, 181},
    {625, 181},
    {630, 181},
    {635, 181},
    {640, 181},
    {645, 181},
    {649, 181},
    {654, 181},
    {659, 181},
    {664, 181},
    {669, 181},
    {674, 181},
    {679, 181},
    {684, 181},
    {688, 181},
    {693, 181},
    {698, 181},
    {703, 181},
    {708, 181},
    {713, 181},
    {718, 181},
    {723, 176},
    {728, 176},
    {732, 176},
    {737, 171},
    {742, 171},
    {747, 166},
    {752, 161},
    {757, 156},
    {762, 151},
    {767, 146},
    {771, 142},
    {771, 137},
    {771, 132},
    {776, 127},
    {776, 122},
    {776, 117},
    {776, 112},
    {781, 107},
    {781, 103},
    {781, 98},
    {781, 93},
    {781, 88},
    {776, 83},
    {776, 78},
    {776, 73},
    {776, 68},
    {771, 63},
    {771, 59},
    {767, 54},
    {767, 49},
    {762, 44},
    {757, 39},
    {752, 34},
    {747, 29},
    {742, 29},
    {737, 24},
    {732, 24},
    {728, 20},
    {723, 20},
    {718, 20},
    {713, 15},
    {708, 15},
    {703, 15},
    {698, 15},
    {693, 15},
    {688, 15},
    {684, 15},
    {679, 15},
    {674, 15},
    {669, 15},
    {664, 15},
    {659, 15},
    {654, 15},
    {649, 15},
    {645, 15},
    {640, 15},
    {635, 15},
    {630, 15},
    {625, 15},
    {620, 15},
    {615, 15},
    {610, 15},
    {605, 15},
    {601, 15},
    {596, 10},
    {596, 5},
    {596, 0},
    {596, -5},
    {596, -10},
    {596, -15},
    {596, -20},
    {596, -24},
    {596, -29},
    {596, -34},
    {596, -39},
    {596, -44},
    {596, -49},
    {596, -54},
    {596, -59},
    {596, -63},
    {596, -68},
    {596, -73},
    {596, -78},
    {596, -83},
    {596, -88},
    {596, -93},
    {596, -98},
    {596, -103},
    {596, -107},
    {596, -112},
    {596, -117},
    {596, -122},
    {596, -127},
    {596, -132},
    {596, -137},
    {596, -142},
    {601, -146},
    {605, -146},
    {610, -146},
    {615, -146},
    {620, -146},
    {625, -146},
    {630, -146},
    {635, -146},
    {640, -146},
    {645, -146},
    {649, -146},
    {654, -146},
    {659, -146},
    {664, -146},
    {669, -146},
    {674, -146},
    {679, -146},
    {684, -146},
    {688, -146},
    {693, -146},
    {698, -146},
    {703, -146},
    {708, -146},
    {713, -146},
    {718, -151},
    {723, -151},
    {728, -151},
    {732, -156},
    {737, -156},
    {742, -161},
    {747, -161},
    {752, -166},
    {757, -171},
    {762, -176},
    {767, -181},
    {767, -186},
    {771, -190},
    {771, -195},
    {776, -200},
    {776, -205},
    {776, -210},
    {776, -215},
    {781, -220},
    {781, -225},
    {781, -229},
    {781, -234},
    {781, -239},
    {776, -244},
    {776, -249},
    {776, -254},
    {776, -259},
    {771, -264},
    {771, -269},
    {767, -273},
    {767, -278},
    {762, -283},
    {757, -288},
    {752, -293},
    {747, -298},
    {742, -303},
    {737, -303},
    {732, -308},
    {728, -308},
    {723, -308},
    {718, -308},
    {713, -312},
    {708, -312},
    {703, -312},
    {698, -312},
    {693, -312},
    {688, -312},
    {684, -312},
    {679, -312},
    {674, -312},
    {669, -312},
    {664, -312},
    {659, -312},
    {654, -312},
    {649, -312},
    {645, -312},
    {640, -312},
    {635, -312},
    {630, -312},
    {625, -312},
    {620, -312},
    {615, -312},
    {610, -312},
    {605, -312},
    {601, -312},
    {596, -317},
    {596, -322},
    {596, -327},
    {596, -332},
    {596, -337},
    {596, -342},
    {596, -347},
    {596, -352},
    {596, -356},
    {596, -361},
    {596, -366},
    {596, -371},
    {596, -376},
    {596, -381},
    {596, -386},
    {596, -391},
    {596, -396},
    {596, -400},
    {596, -405},
    {596, -410},
    {596, -415},
    {596, -420},
    {596, -425},
    {596, -430},
    {596, -435},
    {596, -439},
    {596, -444},
    {596, -449},
    {596, -454},
    {596, -459},
    {596, -464},
    {596, -469},
    {601, -474},
    {605, -474},
    {610, -474},
    {615, -474},
    {620, -474},
    {625, -474},
    {630, -474},
    {635, -474},
    {640, -474},
    {645, -474},
    {649, -474},
    {654, -474},
    {659, -474},
    {664, -474},
    {669, -474},
    {674, -474},
    {679, -474},
    {684, -474},
    {688, -474},
    {693, -474},
    {698, -474},
    {703, -474},
    {708, -474},
    {713, -479},
    {718, -479},
    {723, -479},
    {728, -479},
    {732, -483},
    {737, -483},
    {742, -488},
    {747, -488},
    {752, -493},
    {757, -498},
    {762, -503},
    {767, -508},
    {767, -513},
    {771, -518},
    {771, -522},
    {776, -527},
    {776, -532},
    {776, -537},
    {776, -542},
    {781, -547},
    {781, -552},
    {781, -557},
    {781, -562},
    {781, -566},
    {776, -571},
    {776, -576},
    {776, -581},
    {776, -586},
    {771, -591},
    {771, -596},
    {771, -601},
    {767, -605},
    {762, -610},
    {757, -615},
    {752, -620},
    {747, -625},
    {742, -630},
    {737, -630},
    {732, -635},
    {728, -635},
    {723, -640},
    {718, -640},
    {713, -640},
    {708, -640},
    {703, -640},
    {698, -645},
    {693, -645},
    {688, -645},
    {684, -645},
    {679, -645},
    {674, -645},
    {669, -645},
    {664, -645},
    {659, -645},
    {654, -645},
    {649, -645},
    {645, -645},
    {640, -645},
    {635, -645},
    {630, -645},
    {625, -645},
    {620, -645},
    {615, -645},
    {610, -645},
    {605, -645},
    {601, -645},
    {596, -649},
    {596, -654},
    {596, -659},
    {596, -664},
    {596, -669},
    {596, -674},
    {596, -679},
    {596, -684},
    {596, -688},
    {596, -693},
    {596, -698},
    {596, -703},
    {596, -708},
    {596, -713},
    {596, -718},
    {596, -723},
    {596, -728},
    {596, -732},
    {596, -737},
    {596, -742},
    {596, -747},
    {596, -752},
    {591, -757},
    {591, -762},
    {591, -767},
    {591, -771},
    {586, -776},
    {586, -781},
    {586, -786},
    {581, -791},
    {576, -796},
    {576, -801},
    {571, -806},
    {566, -811},
    {562, -815},
    {557, -820},
    {552, -825},
    {547, -825},
    {542, -830},
    {537, -830},
    {532, -835},
    {527, -835},
    {522, -835},
    {518, -840},
    {513, -840},
    {508, -840},
    {503, -840},
    {498, -845},
    {493, -845},
    {488, -845},
    {483, -845},
    {479, -845},
    {474, -845},
    {469, -845},
    {464, -845},
    {459, -845},
    {454, -845},
    {449, -845},
    {444, -845},
    {439, -845},
    {435, -845},
    {430, -845},
    {425, -845},
    {420, -845},
    {415, -845},
    {410, -845},
    {405, -845},
    {400, -845},
    {396, -845},
    {391, -845},
    {386, -850},
    {386, -854},
    {386, -859},
    {386, -864},
    {386, -869},
    {386, -874},
    {386, -879},
    {386, -884},
    {386, -889},
    {386, -894},
    {386, -898},
    {386, -903},
    {386, -908},
    {386, -913},
    {386, -918},
    {386, -923},
    {386, -928},
    {386, -933},
    {386, -938},
    {386, -942},
    {381, -947},
    {381, -952},
    {381, -957},
    {381, -962},
    {376, -967},
    {376, -972},
    {371, -977},
    {371, -981},
    {366, -986},
    {361, -991},
    {356, -996},
    {352, -996},
    {347, -1001},
    {342, -1006},
    {337, -1006},
    {332, -1006},
    {327, -1011},
    {322, -1011},
    {317, -1011},
    {312, -1011},
    {308, -1011},
    {303, -1011},
    {298, -1011},
    {293, -1011},
    {288, -1011},
    {283, -1011},
    {278, -1011},
    {273, -1006},
    {269, -1006},
    {264, -1006},
    {259, -1001},
    {254, -996},
    {249, -996},
    {244, -991},
    {239, -986},
    {239, -981},
    {234, -977},
    {229, -972},
    {229, -967},
    {229, -962},
    {225, -957},
    {225, -952},
    {225, -947},
    {225, -942},
    {220, -938},
    {220, -933},
    {220, -928},
    {220, -923},
    {220, -918},
    {220, -913},
    {220, -908},
    {220, -903},
    {220, -898},
    {220, -894},
    {220, -889},
    {220, -884},
    {220, -879},
    {220, -874},
    {220, -869},
    {220, -864},
    {220, -859},
    {220, -854},
    {220, -850},
    {220, -845},
    {215, -840},
    {210, -840},
    {205, -840},
    {200, -840},
    {195, -840},
    {190, -840},
    {186, -840},
    {181, -840},
    {176, -840},
    {171, -840},
    {166, -840},
    {161, -840},
    {156, -840},
    {151, -840},
    {146, -840},
    {142, -840},
    {137, -840},
    {132, -840},
    {127, -840},
    {122, -840},
    {117, -840},
    {112, -840},
    {107, -840},
    {103, -840},
    {98, -840},
    {93, -840},
    {88, -840},
    {83, -840},
    {78, -840},
    {73, -840},
    {68, -840},
    {63, -840},
    {59, -840},
    {54, -845},
    {49, -850},
    {49, -854},
    {49, -859},
    {49, -864},
    {49, -869},
    {49, -874},
    {49, -879},
    {49, -884},
    {49, -889},
    {49, -894},
    {49, -898},
    {49, -903},
    {49, -908},
    {49, -913},
    {49, -918},
    {49, -923},
    {49, -928},
    {49, -933},
    {49, -938},
    {49, -942},
    {49, -947},
    {44, -952},
    {44, -957},
    {44, -962},
    {39, -967},
    {39, -972},
    {34, -977},
    {34, -981},
    {29, -986},
    {24, -991},
    {20, -996},
    {15, -1001},
    {10, -1001},
    {5, -1006},
    {0, -1006},
    {-5, -1006},
    {-10, -1011},
    {-15, -1011},
    {-20, -1011},
    {-24, -1011},
    {-29, -1016},
    {-34, -1016},
    {-39, -1011},
    {-44, -1011},
    {-49, -1011},
    {-54, -1011},
    {-59, -1011},
    {-63, -1006},
    {-68, -1006},
    {-73, -1001},
    {-78, -1001},
    {-83, -996},
    {-88, -991},
    {-93, -986},
    {-98, -981},
    {-103, -977},
    {-103, -972},
    {-107, -967},
    {-107, -962},
    {-107, -957},
    {-112, -952},
    {-112, -947},
    {-112, -942},
    {-112, -938},
    {-112, -933},
    {-112, -928},
    {-112, -923},
    {-112, -918},
    {-112, -913},
    {-112, -908},
    {-112, -903},
    {-112, -898},
    {-112, -894},
    {-112, -889},
    {-112, -884},
    {-112, -879},
    {-112, -874},
    {-112, -869},
    {-112, -864},
    {-112, -859},
    {-112, -854},
    {-112, -850},
    {-117, -845},
    {-122, -840},
    {-127, -840},
    {-132, -840},
    {-137, -840},
    {-142, -840},
    {-146, -840},
    {-151, -840},
    {-156, -840},
    {-161, -840},
    {-166, -840},
    {-171, -840},
    {-176, -840},
    {-181, -840},
    {-186, -840},
    {-190, -840},
    {-195, -840},
    {-200, -840},
    {-205, -840},
    {-210, -840},
    {-215, -840},
    {-220, -840},
    {-225, -840},
    {-229, -840},
    {-234, -840},
    {-239, -840},
    {-244, -840},
    {-249, -840},
    {-254, -840},
    {-259, -840},
    {-264, -840},
    {-269, -840},
    {-273, -840},
    {-278, -840},
    {-283, -845},
    {-283, -850},
    {-283, -854},
    {-283, -859},
    {-283, -864},
    {-283, -869},
    {-283, -874},
    {-283, -879},
    {-288, -884},
    {-283, -889},
    {-283, -894},
    {-288, -898},
    {-288, -903},
    {-288, -908},
    {-288, -913},
    {-283, -918},
    {-283, -923},
    {-288, -928},
    {-288, -933},
    {-288, -938},
    {-288, -942},
    {-288, -947},
    {-288, -952},
    {-293, -957},
    {-293, -962},
    {-293, -967},
    {-298, -972},
    {-298, -977},
    {-303, -981},
    {-308, -986},
    {-312, -991},
    {-317, -996},
    {-322, -1001},
    {-327, -1001},
    {-332, -1006},
    {-337, -1006},
    {-342, -1011},
    {-347, -1011},
    {-352, -1011},
    {-356, -1011},
    {-361, -1011},
    {-366, -1016},
    {-381, 698},
    {-381, 703},
    {-381, 708},
    {-381, 713},
    {-381, 718},
    {-381, 723},
    {-386, 728},
    {-391, 728},
    {-396, 728},
    {-400, 728},
    {-405, 728},
    {-410, 728},
    {-415, 728},
    {-420, 728},
    {-425, 728},
    {-430, 728},
    {-435, 728},
    {-439, 728},
    {-444, 728},
    {-449, 728},
    {-454, 728},
    {-459, 728},
    {-464, 728},
    {-469, 728},
    {-474, 728},
    {-474, 732},
    {-474, 737},
    {-474, 742},
    {-474, 747},
    {-474, 752},
    {-474, 757},
    {-474, 762},
    {-474, 767},
    {-474, 771},
    {-474, 776},
    {-474, 781},
    {-474, 786},
    {-474, 791},
    {-474, 796},
    {-474, 801},
    {-474, 806},
    {-474, 811},
    {-474, 815},
    {-474, 820},
    {-474, 825},
    {-474, 830},
    {-474, 835},
    {-474, 840},
    {-474, 845},
    {-474, 850},
    {-474, 854},
    {-474, 859},
    {-474, 864},
    {-474, 869},
    {-474, 874},
    {-474, 879},
    {-474, 884},
    {-474, 889},
    {-474, 894},
    {-474, 898},
    {-469, 903},
    {-464, 903},
    {-459, 903},
    {-454, 903},
    {-449, 903},
    {-444, 903},
    {-439, 903},
    {-435, 903},
    {-430, 903},
    {-425, 903},
    {-420, 903},
    {-415, 903},
    {-410, 903},
    {-405, 903},
    {-400, 903},
    {-396, 903},
    {-391, 903},
    {-386, 903},
    {-381, 908},
    {-381, 913},
    {-381, 918},
    {-381, 923},
    {-381, 928},
    {-381, 933},
    {-381, 938},
    {-381, 942},
    {-376, 947},
    {-376, 952},
    {-371, 957},
    {-366, 962},
    {-361, 962},
    {-356, 962},
    {-352, 967},
    {-347, 967},
    {-342, 967},
    {-337, 967},
    {-332, 967},
    {-327, 967},
    {-322, 967},
    {-317, 967},
    {-312, 967},
    {-308, 967},
    {-303, 967},
    {-298, 967},
    {-293, 967},
    {-288, 967},
    {-283, 967},
    {-278, 967},
    {-273, 967},
    {-269, 967},
    {-264, 967},
    {-259, 967},
    {-254, 967},
    {-249, 967},
    {-244, 967},
    {-239, 967},
    {-234, 962},
    {-229, 962},
    {-225, 962},
    {-220, 957},
    {-220, 952},
    {-215, 947},
    {-215, 942},
    {-215, 938},
    {-210, 933},
    {-210, 928},
    {-210, 923},
    {-210, 918},
    {-210, 913},
    {-210, 908},
    {-210, 903},
    {-210, 898},
    {-215, 898},
    {-220, 894},
    {-225, 894},
    {-229, 894},
    {-234, 889},
    {-239, 889},
    {-244, 884},
    {-244, 879},
    {-244, 874},
    {-244, 869},
    {-244, 864},
    {-244, 859},
    {-244, 854},
    {-244, 850},
    {-244, 845},
    {-244, 840},
    {-244, 835},
    {-244, 830},
    {-244, 825},
    {-244, 820},
    {-244, 815},
    {-244, 811},
    {-244, 806},
    {-244, 801},
    {-244, 796},
    {-244, 791},
    {-244, 786},
    {-244, 781},
    {-244, 776},
    {-244, 771},
    {-244, 767},
    {-244, 762},
    {-244, 757},
    {-244, 752},
    {-244, 747},
    {-244, 742},
    {-244, 737},
    {-249, 732},
    {-249, 728},
    {-254, 728},
    {-259, 728},
    {-264, 728},
    {-269, 728},
    {-273, 728},
    {-278, 728},
    {-283, 728},
    {-288, 728},
    {-293, 728},
    {-298, 728},
    {-303, 728},
    {-308, 728},
    {-312, 728},
    {-317, 728},
    {-322, 728},
    {-327, 728},
    {-332, 728},
    {-337, 728},
    {-342, 723},
    {-342, 718},
    {-342, 713},
    {-342, 708},
    {-342, 703},
    {-342, 698},
    {-347, 698},
    {-352, 698},
    {-356, 698},
    {-361, 698},
    {-366, 698},
    {-371, 698},
    {-376, 698},
    {-381, 703},
    {-376, 698},
    {-371, 698},
    {-366, 698},
    {-361, 698},
    {-356, 698},
    {-352, 698},
    {-347, 698},
    {-342, 703},
    {-342, 708},
    {-342, 713},
    {-342, 718},
    {-342, 723},
    {-342, 728},
    {-337, 728},
    {-332, 728},
    {-327, 728},
    {-322, 728},
    {-317, 728},
    {-312, 728},
    {-308, 728},
    {-303, 728},
    {-298, 728},
    {-293, 728},
    {-288, 728},
    {-283, 728},
    {-278, 728},
    {-273, 728},
    {-269, 728},
    {-264, 728},
    {-259, 728},
    {-254, 728},
    {-249, 732},
    {-244, 737},
    {-244, 742},
    {-244, 747},
    {-244, 752},
    {-244, 757},
    {-244, 762},
    {-244, 767},
    {-244, 771},
    {-244, 776},
    {-244, 781},
    {-244, 786},
    {-244, 791},
    {-244, 796},
    {-244, 801},
    {-244, 806},
    {-244, 811},
    {-244, 815},
    {-244, 820},
    {-244, 825},
    {-244, 830},
    {-244, 835},
    {-244, 840},
    {-244, 845},
    {-244, 850},
    {-244, 854},
    {-244, 859},
    {-244, 864},
    {-244, 869},
    {-244, 874},
    {-244, 879},
    {-244, 884},
    {-244, 889},
    {-239, 889},
    {-234, 889},
    {-229, 894},
    {-225, 894},
    {-220, 894},
    {-215, 898},
    {-210, 903},
    {-210, 908},
    {-210, 913},
    {-210, 918},
    {-210, 923},
    {-210, 928},
    {-210, 933},
    {-215, 938},
    {-215, 942},
    {-220, 947},
    {-220, 952},
    {-225, 957},
    {-229, 962},
    {-234, 962},
    {-239, 967},
    {-244, 967},
    {-249, 967},
    {-254, 967},
    {-259, 967},
    {-264, 967},
    {-269, 967},
    {-273, 967},
    {-278, 967},
    {-283, 967},
    {-288, 967},
    {-293, 967},
    {-298, 967},
    {-303, 967},
    {-308, 967},
    {-312, 967},
    {-317, 967},
    {-322, 967},
    {-327, 967},
    {-332, 967},
    {-337, 967},
    {-342, 967},
    {-347, 967},
    {-352, 967},
    {-356, 962},
    {-361, 962},
    {-366, 957},
    {-371, 952},
    {-376, 947},
    {-381, 942},
    {-381, 938},
    {-381, 933},
    {-381, 928},
    {-381, 923},
    {-381, 918},
    {-381, 913},
    {-381, 908},
    {-381, 903},
    {-386, 903},
    {-391, 903},
    {-396, 903},
    {-400, 903},
    {-405, 903},
    {-410, 903},
    {-415, 903},
    {-420, 903},
    {-425, 903},
    {-430, 903},
    {-435, 903},
    {-439, 903},
    {-444, 903},
    {-449, 903},
    {-454, 903},
    {-459, 903},
    {-464, 903},
    {-469, 898},
    {-474, 894},
    {-474, 889},
    {-474, 884},
    {-474, 879},
    {-474, 874},
    {-474, 869},
    {-474, 864},
    {-474, 859},
    {-474, 854},
    {-474, 850},
    {-474, 845},
    {-474, 840},
    {-474, 835},
    {-474, 830},
    {-474, 825},
    {-474, 820},
    {-474, 815},
    {-474, 811},
    {-474, 806},
    {-474, 801},
    {-474, 796},
    {-474, 791},
    {-474, 786},
    {-474, 781},
    {-474, 776},
    {-474, 771},
    {-474, 767},
    {-474, 762},
    {-474, 757},
    {-474, 752},
    {-474, 747},
    {-474, 742},
    {-474, 737},
    {-474, 732},
    {-469, 728},
    {-464, 728},
    {-459, 728},
    {-454, 728},
    {-449, 728},
    {-444, 728},
    {-439, 728},
    {-435, 728},
    {-430, 728},
    {-425, 728},
    {-420, 728},
    {-415, 728},
    {-410, 728},
    {-405, 728},
    {-400, 728},
    {-396, 728},
    {-391, 728},
    {-386, 728},
    {-381, 728},
    {-381, 723},
    {-381, 718},
    {-381, 713},
    {-381, 708},
    {-337, 762},
    {-342, 767},
    {-342, 771},
    {-342, 776},
    {-342, 781},
    {-342, 786},
    {-342, 791},
    {-342, 796},
    {-337, 796},
    {-332, 796},
    {-327, 796},
    {-322, 796},
    {-317, 796},
    {-312, 796},
    {-308, 796},
    {-303, 796},
    {-298, 796},
    {-293, 796},
    {-288, 796},
    {-283, 796},
    {-283, 791},
    {-283, 786},
    {-283, 781},
    {-283, 776},
    {-283, 771},
    {-283, 767},
    {-288, 762},
    {-293, 762},
    {-298, 762},
    {-303, 762},
    {-308, 762},
    {-312, 762},
    {-317, 762},
    {-322, 762},
    {-327, 762},
    {-332, 762},
    {-337, 767},
    {-332, 762},
    {-327, 762},
    {-322, 762},
    {-317, 762},
    {-312, 762},
    {-308, 762},
    {-303, 762},
    {-298, 762},
    {-293, 762},
    {-288, 767},
    {-283, 771},
    {-283, 776},
    {-283, 781},
    {-283, 786},
    {-283, 791},
    {-288, 796},
    {-293, 796},
    {-298, 796},
    {-303, 796},
    {-308, 796},
    {-312, 796},
    {-317, 796},
    {-322, 796},
    {-327, 796},
    {-332, 796},
    {-337, 796},
    {-342, 791},
    {-342, 786},
    {-342, 781},
    {-342, 776},
    {-342, 771},
    {-337, 830},
    {-342, 835},
    {-342, 840},
    {-342, 845},
    {-342, 850},
    {-342, 854},
    {-342, 859},
    {-342, 864},
    {-337, 864},
    {-332, 869},
    {-327, 869},
    {-322, 869},
    {-317, 869},
    {-312, 869},
    {-308, 869},
    {-303, 869},
    {-298, 869},
    {-293, 869},
    {-288, 864},
    {-283, 864},
    {-283, 859},
    {-283, 854},
    {-283, 850},
    {-283, 845},
    {-283, 840},
    {-283, 835},
    {-288, 830},
    {-293, 830},
    {-298, 830},
    {-303, 830},
    {-308, 830},
    {-312, 830},
    {-317, 830},
    {-322, 830},
    {-327, 830},
    {-332, 830},
    {-337, 835},
    {-332, 830},
    {-327, 830},
    {-322, 830},
    {-317, 830},
    {-312, 830},
    {-308, 830},
    {-303, 830},
    {-298, 830},
    {-293, 830},
    {-288, 835},
    {-283, 840},
    {-283, 845},
    {-283, 850},
    {-283, 854},
    {-283, 859},
    {-288, 864},
    {-293, 869},
    {-298, 869},
    {-303, 869},
    {-308, 869},
    {-312, 869},
    {-317, 869},
    {-322, 869},
    {-327, 869},
    {-332, 869},
    {-337, 864},
    {-342, 859},
    {-342, 854},
    {-342, 850},
    {-342, 845},
    {-342, 840},
    {-342, 903},
    {-342, 908},
    {-342, 913},
    {-342, 918},
    {-342, 923},
    {-342, 928},
    {-337, 928},
    {-332, 928},
    {-327, 933},
    {-322, 933},
    {-317, 933},
    {-312, 933},
    {-308, 933},
    {-303, 933},
    {-298, 933},
    {-293, 933},
    {-288, 933},
    {-283, 933},
    {-278, 933},
    {-273, 933},
    {-269, 933},
    {-264, 928},
    {-259, 928},
    {-254, 928},
    {-254, 923},
    {-254, 918},
    {-254, 913},
    {-254, 908},
    {-254, 903},
    {-259, 903},
    {-264, 903},
    {-269, 903},
    {-273, 903},
    {-278, 903},
    {-283, 903},
    {-288, 903},
    {-293, 903},
    {-298, 903},
    {-303, 903},
    {-308, 903},
    {-312, 903},
    {-317, 903},
    {-322, 903},
    {-327, 903},
    {-332, 903},
    {-337, 903},
    {-342, 908},
    {-337, 903},
    {-332, 903},
    {-327, 903},
    {-322, 903},
    {-317, 903},
    {-312, 903},
    {-308, 903},
    {-303, 903},
    {-298, 903},
    {-293, 903},
    {-288, 903},
    {-283, 903},
    {-278, 903},
    {-273, 903},
    {-269, 903},
    {-264, 903},
    {-259, 903},
    {-254, 908},
    {-254, 913},
    {-254, 918},
    {-254, 923},
    {-259, 928},
    {-264, 928},
    {-269, 933},
    {-273, 933},
    {-278, 933},
    {-283, 933},
    {-288, 933},
    {-293, 933},
    {-298, 933},
    {-303, 933},
    {-308, 933},
    {-312, 933},
    {-317, 933},
    {-322, 933},
    {-327, 933},
    {-332, 928},
    {-337, 928},
    {-342, 923},
    {-342, 918},
    {-342, 913},
    {-435, 835},
    {-430, 830},
    {-425, 830},
    {-420, 830},
    {-415, 830},
    {-410, 830},
    {-405, 830},
    {-400, 830},
    {-396, 830},
    {-391, 830},
    {-386, 835},
    {-381, 840},
    {-381, 845},
    {-381, 850},
    {-381, 854},
    {-381, 859},
    {-386, 864},
    {-391, 869},
    {-396, 869},
    {-400, 869},
    {-405, 869},
    {-410, 869},
    {-415, 869},
    {-420, 869},
    {-425, 869},
    {-430, 869},
    {-435, 864},
    {-435, 859},
    {-435, 854},
    {-435, 850},
    {-435, 845},
    {-435, 840},
    {-435, 830},
    {-435, 835},
    {-435, 840},
    {-435, 845},
    {-435, 850},
    {-435, 854},
    {-435, 859},
    {-435, 864},
    {-435, 869},
    {-430, 869},
    {-425, 869},
    {-420, 869},
    {-415, 869},
    {-410, 869},
    {-405, 869},
    {-400, 869},
    {-396, 869},
    {-391, 869},
    {-386, 864},
    {-381, 864},
    {-381, 859},
    {-381, 854},
    {-381, 850},
    {-381, 845},
    {-381, 840},
    {-381, 835},
    {-386, 830},
    {-391, 830},
    {-396, 830},
    {-400, 830},
    {-405, 830},
    {-410, 830},
    {-415, 830},
    {-420, 830},
    {-425, 830},
    {-430, 830},
    {-435, 767},
    {-430, 762},
    {-425, 762},
    {-420, 762},
    {-415, 762},
    {-410, 762},
    {-405, 762},
    {-400, 762},
    {-396, 762},
    {-391, 762},
    {-386, 767},
    {-381, 771},
    {-381, 776},
    {-381, 781},
    {-381, 786},
    {-381, 791},
    {-386, 796},
    {-391, 796},
    {-396, 796},
    {-400, 796},
    {-405, 796},
    {-410, 796},
    {-415, 796},
    {-420, 796},
    {-425, 796},
    {-430, 796},
    {-435, 791},
    {-435, 786},
    {-435, 781},
    {-435, 776},
    {-435, 771},
    {-435, 762},
    {-435, 767},
    {-435, 771},
    {-435, 776},
    {-435, 781},
    {-435, 786},
    {-435, 791},
    {-435, 796},
    {-430, 796},
    {-425, 796},
    {-420, 796},
    {-415, 796},
    {-410, 796},
    {-405, 796},
    {-400, 796},
    {-396, 796},
    {-391, 796},
    {-386, 796},
    {-381, 796},
    {-381, 791},
    {-381, 786},
    {-381, 781},
    {-381, 776},
    {-381, 771},
    {-381, 767},
    {-386, 762},
    {-391, 762},
    {-396, 762},
    {-400, 762},
    {-405, 762},
    {-410, 762},
    {-415, 762},
    {-420, 762},
    {-425, 762},
    {-430, 762},
    {-596, 762},
    {-601, 767},
    {-605, 767},
    {-605, 771},
    {-605, 776},
    {-605, 781},
    {-605, 786},
    {-601, 786},
    {-596, 786},
    {-591, 786},
    {-586, 786},
    {-586, 781},
    {-586, 776},
    {-586, 771},
    {-591, 767},
    {-601, 767},
    {-596, 762},
    {-591, 767},
    {-586, 771},
    {-586, 776},
    {-586, 781},
    {-591, 786},
    {-596, 786},
    {-601, 786},
    {-605, 781},
    {-605, 776},
    {-605, 771},
    {-645, 835},
    {-649, 840},
    {-649, 845},
    {-649, 850},
    {-649, 854},
    {-649, 859},
    {-649, 864},
    {-649, 869},
    {-649, 874},
    {-649, 879},
    {-649, 884},
    {-649, 889},
    {-649, 894},
    {-649, 898},
    {-649, 903},
    {-649, 908},
    {-649, 913},
    {-649, 918},
    {-649, 923},
    {-649, 928},
    {-649, 933},
    {-649, 938},
    {-649, 942},
    {-649, 947},
    {-649, 952},
    {-649, 957},
    {-649, 962},
    {-645, 962},
    {-640, 967},
    {-635, 967},
    {-630, 967},
    {-625, 967},
    {-620, 967},
    {-615, 967},
    {-610, 967},
    {-605, 967},
    {-601, 967},
    {-596, 967},
    {-591, 967},
    {-586, 967},
    {-581, 967},
    {-576, 967},
    {-571, 967},
    {-566, 967},
    {-562, 967},
    {-557, 967},
    {-552, 967},
    {-547, 967},
    {-542, 962},
    {-537, 962},
    {-537, 957},
    {-537, 952},
    {-537, 947},
    {-537, 942},
    {-537, 938},
    {-537, 933},
    {-537, 928},
    {-537, 923},
    {-537, 918},
    {-537, 913},
    {-537, 908},
    {-537, 903},
    {-537, 898},
    {-537, 894},
    {-537, 889},
    {-537, 884},
    {-537, 879},
    {-537, 874},
    {-537, 869},
    {-537, 864},
    {-537, 859},
    {-537, 854},
    {-537, 850},
    {-537, 845},
    {-542, 840},
    {-547, 835},
    {-552, 835},
    {-557, 835},
    {-562, 835},
    {-566, 835},
    {-571, 835},
    {-576, 835},
    {-581, 835},
    {-586, 835},
    {-591, 835},
    {-596, 835},
    {-601, 835},
    {-605, 835},
    {-610, 835},
    {-615, 835},
    {-620, 835},
    {-625, 835},
    {-630, 835},
    {-635, 835},
    {-640, 835},
    {-645, 840},
    {-640, 835},
    {-635, 835},
    {-630, 835},
    {-625, 835},
    {-620, 835},
    {-615, 835},
    {-610, 835},
    {-605, 835},
    {-601, 835},
    {-596, 835},
    {-591, 835},
    {-586, 835},
    {-581, 835},
    {-576, 835},
    {-571, 835},
    {-566, 835},
    {-562, 835},
    {-557, 835},
    {-552, 835},
    {-547, 835},
    {-542, 840},
    {-537, 845},
    {-537, 850},
    {-537, 854},
    {-537, 859},
    {-537, 864},
    {-537, 869},
    {-537, 874},
    {-537, 879},
    {-537, 884},
    {-537, 889},
    {-537, 894},
    {-537, 898},
    {-537, 903},
    {-537, 908},
    {-537, 913},
    {-537, 918},
    {-537, 923},
    {-537, 928},
    {-537, 933},
    {-537, 938},
    {-537, 942},
    {-537, 947},
    {-537, 952},
    {-537, 957},
    {-542, 962},
    {-547, 967},
    {-552, 967},
    {-557, 967},
    {-562, 967},
    {-566, 967},
    {-571, 967},
    {-576, 967},
    {-581, 967},
    {-586, 967},
    {-591, 967},
    {-596, 967},
    {-601, 967},
    {-605, 967},
    {-610, 967},
    {-615, 967},
    {-620, 967},
    {-625, 967},
    {-630, 967},
    {-635, 967},
    {-640, 967},
    {-645, 962},
    {-649, 957},
    {-649, 952},
    {-649, 947},
    {-649, 942},
    {-649, 938},
    {-649, 933},
    {-649, 928},
    {-649, 923},
    {-649, 918},
    {-649, 913},
    {-649, 908},
    {-649, 903},
    {-649, 898},
    {-649, 894},
    {-649, 889},
    {-649, 884},
    {-649, 879},
    {-649, 874},
    {-649, 869},
    {-649, 864},
    {-649, 859},
    {-649, 854},
    {-649, 850},
    {-649, 845},
    {-615, 869},
    {-610, 864},
    {-605, 864},
    {-601, 864},
    {-596, 864},
    {-591, 864},
    {-586, 864},
    {-581, 864},
    {-576, 864},
    {-571, 869},
    {-571, 874},
    {-571, 879},
    {-576, 884},
    {-581, 884},
    {-586, 884},
    {-591, 884},
    {-596, 884},
    {-601, 884},
    {-605, 884},
    {-610, 884},
    {-615, 884},
    {-620, 879},
    {-620, 874},
    {-610, 864},
    {-615, 869},
    {-620, 869},
    {-620, 874},
    {-620, 879},
    {-620, 884},
    {-615, 884},
    {-610, 884},
    {-605, 884},
    {-601, 884},
    {-596, 884},
    {-591, 884},
    {-586, 884},
    {-581, 884},
    {-576, 884},
    {-571, 884},
    {-571, 879},
    {-571, 874},
    {-571, 869},
    {-576, 864},
    {-581, 864},
    {-586, 864},
    {-591, 864},
    {-596, 864},
    {-601, 864},
    {-605, 864},
    {-620, 918},
    {-620, 923},
    {-620, 928},
    {-620, 933},
    {-615, 933},
    {-610, 933},
    {-605, 933},
    {-601, 933},
    {-596, 933},
    {-591, 933},
    {-586, 933},
    {-581, 933},
    {-576, 933},
    {-571, 933},
    {-571, 928},
    {-571, 923},
    {-571, 918},
    {-576, 918},
    {-581, 918},
    {-586, 918},
    {-591, 918},
    {-596, 918},
    {-601, 918},
    {-605, 918},
    {-610, 918},
    {-615, 918},
    {-620, 923},
    {-615, 918},
    {-610, 918},
    {-605, 918},
    {-601, 918},
    {-596, 918},
    {-591, 918},
    {-586, 918},
    {-581, 918},
    {-576, 918},
    {-571, 923},
    {-571, 928},
    {-576, 933},
    {-581, 933},
    {-586, 933},
    {-591, 933},
    {-596, 933},
    {-601, 933},
    {-605, 933},
    {-610, 933},
    {-615, 933},
    {-620, 928},
    {-747, 908},
    {-752, 913},
    {-757, 913},
    {-757, 918},
    {-757, 923},
    {-757, 928},
    {-752, 928},
    {-747, 928},
    {-742, 928},
    {-737, 928},
    {-732, 928},
    {-728, 928},
    {-723, 928},
    {-718, 928},
    {-713, 928},
    {-708, 928},
    {-703, 928},
    {-698, 928},
    {-698, 923},
    {-698, 918},
    {-698, 913},
    {-703, 913},
    {-708, 908},
    {-713, 908},
    {-718, 908},
    {-723, 908},
    {-728, 908},
    {-732, 908},
    {-737, 908},
    {-742, 908},
    {-752, 913},
    {-747, 908},
    {-742, 908},
    {-737, 908},
    {-732, 908},
    {-728, 908},
    {-723, 908},
    {-718, 908},
    {-713, 908},
    {-708, 908},
    {-703, 913},
    {-698, 918},
    {-698, 923},
    {-703, 928},
    {-708, 928},
    {-713, 928},
    {-718, 928},
    {-723, 928},
    {-728, 928},
    {-732, 928},
    {-737, 928},
    {-742, 928},
    {-747, 928},
    {-752, 928},
    {-757, 923},
    {-757, 918},
    {-762, 801},
    {-757, 796},
    {-752, 801},
    {-752, 806},
    {-752, 811},
    {-752, 815},
    {-752, 820},
    {-752, 825},
    {-752, 830},
    {-757, 835},
    {-762, 830},
    {-762, 825},
    {-762, 820},
    {-762, 815},
    {-762, 811},
    {-762, 806},
    {-757, 796},
    {-762, 801},
    {-762, 806},
    {-762, 811},
    {-762, 815},
    {-762, 820},
    {-762, 825},
    {-762, 830},
    {-762, 835},
    {-757, 835},
    {-752, 835},
    {-752, 830},
    {-752, 825},
    {-752, 820},
    {-752, 815},
    {-752, 811},
    {-752, 806},
    {-752, 801},
    {-752, 796},
    {-732, 801},
    {-737, 806},
    {-737, 811},
    {-737, 815},
    {-737, 820},
    {-737, 815},
    {-737, 811},
    {-737, 806},
    {-732, 801},
    {-728, 806},
    {-728, 811},
    {-728, 815},
    {-728, 820},
    {-728, 825},
    {-728, 830},
    {-728, 835},
    {-728, 840},
    {-728, 845},
    {-723, 850},
    {-723, 854},
    {-723, 859},
    {-718, 864},
    {-713, 864},
    {-708, 869},
    {-703, 869},
    {-698, 874},
    {-703, 879},
    {-708, 879},
    {-713, 879},
    {-718, 879},
    {-723, 879},
    {-728, 879},
    {-732, 879},
    {-737, 879},
    {-742, 879},
    {-747, 879},
    {-752, 879},
    {-757, 874},
    {-752, 869},
    {-747, 864},
    {-742, 864},
    {-737, 859},
    {-737, 854},
    {-737, 850},
    {-737, 845},
    {-737, 850},
    {-737, 854},
    {-742, 859},
    {-747, 864},
    {-752, 869},
    {-757, 869},
    {-757, 874},
    {-757, 879},
    {-752, 879},
    {-747, 879},
    {-742, 879},
    {-737, 879},
    {-732, 879},
    {-728, 879},
    {-723, 879},
    {-718, 879},
    {-713, 879},
    {-708, 879},
    {-703, 879},
    {-698, 879},
    {-698, 874},
    {-698, 869},
    {-703, 869},
    {-708, 869},
    {-713, 864},
    {-718, 859},
    {-723, 854},
    {-723, 850},
    {-728, 845},
    {-728, 840},
    {-728, 835},
    {-728, 830},
    {-728, 825},
    {-728, 820},
    {-728, 815},
    {-728, 811},
    {-728, 806},
    {-728, 801},
    {-708, 801},
    {-703, 796},
    {-698, 801},
    {-698, 806},
    {-698, 811},
    {-698, 815},
    {-698, 820},
    {-703, 825},
    {-708, 820},
    {-708, 815},
    {-708, 811},
    {-708, 806},
    {-708, 796},
    {-708, 801},
    {-708, 806},
    {-708, 811},
    {-708, 815},
    {-708, 820},
    {-703, 825},
    {-703, 830},
    {-703, 825},
    {-698, 820},
    {-698, 815},
    {-698, 811},
    {-698, 806},
    {-698, 801},
    {-698, 796},
    {-703, 796},
    {-732, 742},
    {-732, 747},
    {-732, 752},
    {-728, 757},
    {-732, 762},
    {-728, 762},
    {-728, 757},
    {-732, 752},
    {-732, 747},
    {-728, 742},
    {-781, 708},
    {-786, 713},
    {-791, 718},
    {-791, 723},
    {-791, 728},
    {-791, 732},
    {-786, 737},
    {-786, 742},
    {-781, 742},
    {-776, 742},
    {-771, 742},
    {-767, 742},
    {-762, 742},
    {-757, 747},
    {-757, 752},
    {-757, 757},
    {-762, 762},
    {-767, 762},
    {-771, 762},
    {-776, 762},
    {-781, 767},
    {-786, 771},
    {-786, 776},
    {-786, 781},
    {-786, 786},
    {-786, 791},
    {-786, 796},
    {-786, 801},
    {-786, 806},
    {-786, 811},
    {-786, 815},
    {-786, 820},
    {-786, 825},
    {-786, 830},
    {-786, 835},
    {-786, 840},
    {-786, 845},
    {-786, 850},
    {-786, 854},
    {-786, 859},
    {-786, 864},
    {-786, 869},
    {-786, 874},
    {-786, 879},
    {-786, 884},
    {-786, 889},
    {-786, 894},
    {-786, 898},
    {-786, 903},
    {-786, 908},
    {-786, 913},
    {-786, 918},
    {-786, 923},
    {-786, 928},
    {-786, 933},
    {-786, 938},
    {-786, 942},
    {-786, 947},
    {-786, 952},
    {-781, 957},
    {-776, 962},
    {-771, 962},
    {-767, 962},
    {-762, 962},
    {-757, 962},
    {-752, 962},
    {-747, 962},
    {-742, 962},
    {-737, 962},
    {-732, 962},
    {-728, 962},
    {-723, 962},
    {-718, 962},
    {-713, 962},
    {-708, 962},
    {-703, 962},
    {-698, 962},
    {-693, 962},
    {-688, 962},
    {-684, 962},
    {-679, 962},
    {-674, 957},
    {-669, 957},
    {-669, 952},
    {-669, 947},
    {-669, 942},
    {-669, 938},
    {-669, 933},
    {-669, 928},
    {-669, 923},
    {-669, 918},
    {-669, 913},
    {-669, 908},
    {-669, 903},
    {-669, 898},
    {-669, 894},
    {-669, 889},
    {-669, 884},
    {-669, 879},
    {-669, 874},
    {-669, 869},
    {-669, 864},
    {-669, 859},
    {-669, 854},
    {-669, 850},
    {-669, 845},
    {-669, 840},
    {-669, 835},
    {-669, 830},
    {-669, 825},
    {-669, 820},
    {-669, 815},
    {-669, 811},
    {-669, 806},
    {-669, 801},
    {-669, 796},
    {-669, 791},
    {-669, 786},
    {-669, 781},
    {-669, 776},
    {-669, 771},
    {-669, 767},
    {-674, 767},
    {-679, 762},
    {-684, 762},
    {-688, 762},
    {-693, 762},
    {-698, 762},
    {-703, 757},
    {-703, 752},
    {-698, 747},
    {-693, 742},
    {-688, 742},
    {-684, 742},
    {-679, 742},
    {-674, 742},
    {-674, 737},
    {-669, 732},
    {-669, 728},
    {-669, 723},
    {-669, 718},
    {-674, 713},
    {-679, 708},
    {-684, 708},
    {-688, 708},
    {-693, 708},
    {-698, 708},
    {-703, 708},
    {-708, 708},
    {-713, 708},
    {-718, 708},
    {-723, 708},
    {-728, 708},
    {-732, 708},
    {-737, 708},
    {-742, 708},
    {-747, 708},
    {-752, 708},
    {-757, 708},
    {-762, 708},
    {-767, 708},
    {-771, 708},
    {-776, 708},
    {-786, 713},
    {-781, 708},
    {-776, 708},
    {-771, 708},
    {-767, 708},
    {-762, 708},
    {-757, 708},
    {-752, 708},
    {-747, 708},
    {-742, 708},
    {-737, 708},
    {-732, 708},
    {-728, 708},
    {-723, 708},
    {-718, 708},
    {-713, 708},
    {-708, 708},
    {-703, 708},
    {-698, 708},
    {-693, 708},
    {-688, 708},
    {-684, 708},
    {-679, 708},
    {-674, 713},
    {-669, 718},
    {-669, 723},
    {-669, 728},
    {-669, 732},
    {-674, 737},
    {-679, 742},
    {-684, 742},
    {-688, 742},
    {-693, 742},
    {-698, 742},
    {-703, 747},
    {-703, 752},
    {-703, 757},
    {-703, 762},
    {-698, 762},
    {-693, 762},
    {-688, 762},
    {-684, 762},
    {-679, 762},
    {-674, 767},
    {-669, 771},
    {-669, 776},
    {-669, 781},
    {-669, 786},
    {-669, 791},
    {-669, 796},
    {-669, 801},
    {-669, 806},
    {-669, 811},
    {-669, 815},
    {-669, 820},
    {-669, 825},
    {-669, 830},
    {-669, 835},
    {-669, 840},
    {-669, 845},
    {-669, 850},
    {-669, 854},
    {-669, 859},
    {-669, 864},
    {-669, 869},
    {-669, 874},
    {-669, 879},
    {-669, 884},
    {-669, 889},
    {-669, 894},
    {-669, 898},
    {-669, 903},
    {-669, 908},
    {-669, 913},
    {-669, 918},
    {-669, 923},
    {-669, 928},
    {-669, 933},
    {-669, 938},
    {-669, 942},
    {-669, 947},
    {-669, 952},
    {-674, 957},
    {-679, 962},
    {-684, 962},
    {-688, 962},
    {-693, 962},
    {-698, 962},
    {-703, 962},
    {-708, 962},
    {-713, 962},
    {-718, 962},
    {-723, 962},
    {-728, 962},
    {-732, 962},
    {-737, 962},
    {-742, 962},
    {-747, 962},
    {-752, 962},
    {-757, 962},
    {-762, 962},
    {-767, 962},
    {-771, 962},
    {-776, 962},
    {-781, 957},
    {-786, 952},
    {-786, 947},
    {-786, 942},
    {-786, 938},
    {-786, 933},
    {-786, 928},
    {-786, 923},
    {-786, 918},
    {-786, 913},
    {-786, 908},
    {-786, 903},
    {-786, 898},
    {-786, 894},
    {-786, 889},
    {-786, 884},
    {-786, 879},
    {-786, 874},
    {-786, 869},
    {-786, 864},
    {-786, 859},
    {-786, 854},
    {-786, 850},
    {-786, 845},
    {-786, 840},
    {-786, 835},
    {-786, 830},
    {-786, 825},
    {-786, 820},
    {-786, 815},
    {-786, 811},
    {-786, 806},
    {-786, 801},
    {-786, 796},
    {-786, 791},
    {-786, 786},
    {-786, 781},
    {-786, 776},
    {-786, 771},
    {-781, 767},
    {-776, 762},
    {-771, 762},
    {-767, 762},
    {-762, 762},
    {-757, 762},
    {-757, 757},
    {-757, 752},
    {-757, 747},
    {-757, 742},
    {-762, 742},
    {-767, 742},
    {-771, 742},
    {-776, 742},
    {-781, 742},
    {-786, 737},
    {-791, 732},
    {-791, 728},
    {-791, 723},
    {-791, 718},
    {-884, 718},
    {-879, 713},
    {-874, 713},
    {-869, 718},
    {-864, 723},
    {-859, 728},
    {-854, 732},
    {-850, 737},
    {-854, 742},
    {-859, 747},
    {-864, 752},
    {-869, 757},
    {-874, 762},
    {-874, 767},
    {-879, 771},
    {-884, 776},
    {-889, 781},
    {-894, 786},
    {-898, 791},
    {-903, 791},
    {-908, 786},
    {-913, 781},
    {-918, 776},
    {-923, 771},
    {-928, 767},
    {-923, 762},
    {-918, 757},
    {-913, 752},
    {-908, 747},
    {-903, 742},
    {-898, 737},
    {-894, 732},
    {-894, 728},
    {-889, 723},
    {-903, 801},
    {-898, 801},
    {-933, 835},
    {-928, 835},
    {-1025, 801},
    {-1021, 801},
    {-1055, 718},
    {-1050, 713},
    {-1045, 718},
    {-1040, 723},
    {-1035, 728},
    {-1030, 732},
    {-1025, 737},
    {-1021, 742},
    {-1016, 747},
    {-1011, 752},
    {-1006, 757},
    {-1001, 762},
    {-996, 767},
    {-1001, 771},
    {-1006, 776},
    {-1011, 781},
    {-1016, 786},
    {-1021, 791},
    {-1025, 791},
    {-1030, 786},
    {-1035, 781},
    {-1040, 776},
    {-1040, 771},
    {-1045, 767},
    {-1050, 762},
    {-1055, 757},
    {-1060, 752},
    {-1064, 747},
    {-1069, 742},
    {-1074, 737},
    {-1069, 732},
    {-1064, 728},
    {-1060, 723},
    {-981, 703},
    {-977, 698},
    {-972, 698},
    {-967, 698},
    {-962, 698},
    {-957, 698},
    {-952, 698},
    {-947, 698},
    {-942, 703},
    {-942, 708},
    {-942, 713},
    {-942, 718},
    {-942, 723},
    {-942, 728},
    {-942, 732},
    {-942, 737},
    {-942, 742},
    {-942, 747},
    {-942, 752},
    {-942, 757},
    {-942, 762},
    {-942, 767},
    {-942, 771},
    {-942, 776},
    {-942, 781},
    {-942, 786},
    {-942, 791},
    {-942, 796},
    {-938, 796},
    {-933, 796},
    {-928, 796},
    {-923, 796},
    {-918, 796},
    {-913, 796},
    {-908, 791},
    {-903, 791},
    {-898, 791},
    {-894, 791},
    {-889, 796},
    {-884, 796},
    {-879, 796},
    {-874, 796},
    {-869, 796},
    {-864, 796},
    {-859, 796},
    {-854, 796},
    {-850, 796},
    {-845, 796},
    {-840, 801},
    {-835, 806},
    {-835, 811},
    {-835, 815},
    {-835, 820},
    {-835, 825},
    {-835, 830},
    {-840, 835},
    {-845, 840},
    {-850, 840},
    {-854, 840},
    {-859, 840},
    {-864, 840},
    {-869, 840},
    {-874, 840},
    {-879, 840},
    {-884, 840},
    {-889, 840},
    {-894, 840},
    {-898, 840},
    {-903, 840},
    {-908, 840},
    {-913, 840},
    {-918, 840},
    {-923, 845},
    {-928, 845},
    {-933, 845},
    {-928, 845},
    {-923, 850},
    {-918, 854},
    {-913, 859},
    {-908, 864},
    {-903, 869},
    {-898, 869},
    {-894, 874},
    {-889, 879},
    {-884, 879},
    {-879, 884},
    {-874, 889},
    {-869, 894},
    {-864, 894},
    {-859, 898},
    {-854, 898},
    {-850, 903},
    {-845, 903},
    {-840, 908},
    {-835, 913},
    {-830, 913},
    {-825, 918},
    {-825, 923},
    {-825, 928},
    {-830, 933},
    {-835, 938},
    {-835, 942},
    {-840, 947},
    {-845, 952},
    {-850, 947},
    {-854, 947},
    {-859, 942},
    {-864, 942},
    {-869, 938},
    {-874, 933},
    {-879, 933},
    {-884, 928},
    {-889, 923},
    {-894, 923},
    {-898, 918},
    {-903, 913},
    {-908, 908},
    {-913, 908},
    {-918, 903},
    {-923, 898},
    {-928, 894},
    {-933, 889},
    {-938, 889},
    {-942, 889},
    {-942, 894},
    {-942, 898},
    {-942, 903},
    {-942, 908},
    {-942, 913},
    {-942, 918},
    {-942, 923},
    {-942, 928},
    {-942, 933},
    {-942, 938},
    {-942, 942},
    {-942, 947},
    {-942, 952},
    {-942, 957},
    {-947, 962},
    {-952, 967},
    {-957, 967},
    {-962, 967},
    {-967, 967},
    {-972, 967},
    {-977, 962},
    {-981, 957},
    {-981, 952},
    {-981, 947},
    {-981, 942},
    {-981, 938},
    {-981, 933},
    {-981, 928},
    {-981, 923},
    {-981, 918},
    {-981, 913},
    {-981, 908},
    {-981, 903},
    {-981, 898},
    {-981, 894},
    {-981, 889},
    {-981, 884},
    {-986, 884},
    {-991, 884},
    {-991, 889},
    {-996, 894},
    {-1001, 894},
    {-1006, 898},
    {-1011, 903},
    {-1016, 908},
    {-1021, 913},
    {-1025, 918},
    {-1030, 918},
    {-1035, 923},
    {-1040, 928},
    {-1045, 928},
    {-1050, 933},
    {-1055, 938},
    {-1060, 938},
    {-1064, 942},
    {-1069, 947},
    {-1074, 947},
    {-1079, 952},
    {-1084, 947},
    {-1089, 942},
    {-1089, 938},
    {-1094, 933},
    {-1099, 928},
    {-1104, 923},
    {-1104, 918},
    {-1099, 913},
    {-1094, 913},
    {-1089, 908},
    {-1084, 908},
    {-1079, 903},
    {-1074, 903},
    {-1069, 898},
    {-1064, 894},
    {-1060, 894},
    {-1055, 889},
    {-1050, 884},
    {-1045, 884},
    {-1040, 879},
    {-1035, 874},
    {-1030, 874},
    {-1025, 869},
    {-1021, 864},
    {-1016, 859},
    {-1011, 854},
    {-1006, 850},
    {-1001, 850},
    {-996, 845},
    {-1001, 840},
    {-996, 835},
    {-1001, 835},
    {-1006, 840},
    {-1011, 840},
    {-1016, 840},
    {-1021, 840},
    {-1025, 840},
    {-1030, 840},
    {-1035, 840},
    {-1040, 840},
    {-1045, 840},
    {-1050, 840},
    {-1055, 840},
    {-1060, 840},
    {-1064, 840},
    {-1069, 840},
    {-1074, 840},
    {-1079, 840},
    {-1084, 840},
    {-1089, 835},
    {-1094, 830},
    {-1094, 825},
    {-1094, 820},
    {-1094, 815},
    {-1094, 811},
    {-1094, 806},
    {-1089, 801},
    {-1084, 796},
    {-1079, 796},
    {-1074, 796},
    {-1069, 796},
    {-1064, 796},
    {-1060, 796},
    {-1055, 796},
    {-1050, 796},
    {-1045, 796},
    {-1040, 796},
    {-1035, 796},
    {-1030, 791},
    {-1025, 791},
    {-1021, 791},
    {-1016, 791},
    {-1011, 796},
    {-1006, 796},
    {-1001, 796},
    {-996, 796},
    {-991, 796},
    {-986, 796},
    {-981, 796},
    {-981, 791},
    {-981, 786},
    {-981, 781},
    {-981, 776},
    {-981, 771},
    {-981, 767},
    {-981, 762},
    {-981, 757},
    {-981, 752},
    {-981, 747},
    {-981, 742},
    {-981, 737},
    {-981, 732},
    {-981, 728},
    {-981, 723},
    {-981, 718},
    {-981, 713},
    {-981, 708},
    {-981, 698},
    {-981, 703},
    {-981, 708},
    {-981, 713},
    {-981, 718},
    {-981, 723},
    {-981, 728},
    {-981, 732},
    {-981, 737},
    {-981, 742},
    {-981, 747},
    {-981, 752},
    {-981, 757},
    {-981, 762},
    {-981, 767},
    {-981, 771},
    {-981, 776},
    {-981, 781},
    {-981, 786},
    {-981, 791},
    {-986, 796},
    {-991, 796},
    {-996, 796},
    {-1001, 796},
    {-1006, 796},
    {-1011, 796},
    {-1016, 791},
    {-1011, 786},
    {-1006, 781},
    {-1001, 776},
    {-996, 771},
    {-996, 767},
    {-1001, 762},
    {-1001, 757},
    {-1006, 752},
    {-1011, 747},
    {-1016, 742},
    {-1021, 737},
    {-1025, 732},
    {-1030, 728},
    {-1035, 723},
    {-1040, 718},
    {-1045, 713},
    {-1050, 713},
    {-1055, 713},
    {-1060, 718},
    {-1064, 723},
    {-1069, 728},
    {-1074, 732},
    {-1074, 737},
    {-1074, 742},
    {-1069, 747},
    {-1064, 752},
    {-1060, 757},
    {-1055, 762},
    {-1050, 767},
    {-1045, 771},
    {-1040, 776},
    {-1035, 781},
    {-1035, 786},
    {-1030, 791},
    {-1035, 796},
    {-1040, 796},
    {-1045, 796},
    {-1050, 796},
    {-1055, 796},
    {-1060, 796},
    {-1064, 796},
    {-1069, 796},
    {-1074, 796},
    {-1079, 796},
    {-1084, 796},
    {-1089, 801},
    {-1094, 806},
    {-1094, 811},
    {-1094, 815},
    {-1094, 820},
    {-1094, 825},
    {-1094, 830},
    {-1089, 835},
    {-1084, 840},
    {-1079, 840},
    {-1074, 840},
    {-1069, 840},
    {-1064, 840},
    {-1060, 840},
    {-1055, 840},
    {-1050, 840},
    {-1045, 840},
    {-1040, 840},
    {-1035, 840},
    {-1030, 840},
    {-1025, 840},
    {-1021, 840},
    {-1016, 840},
    {-1011, 840},
    {-1006, 840},
    {-1001, 845},
    {-1006, 850},
    {-1011, 850},
    {-1016, 854},
    {-1021, 859},
    {-1025, 864},
    {-1030, 869},
    {-1035, 874},
    {-1040, 874},
    {-1045, 879},
    {-1050, 884},
    {-1055, 884},
    {-1060, 889},
    {-1064, 894},
    {-1069, 894},
    {-1074, 898},
    {-1079, 903},
    {-1084, 903},
    {-1089, 908},
    {-1094, 908},
    {-1099, 913},
    {-1104, 913},
    {-1104, 918},
    {-1104, 923},
    {-1104, 928},
    {-1099, 933},
    {-1094, 938},
    {-1089, 942},
    {-1089, 947},
    {-1084, 952},
    {-1079, 952},
    {-1074, 952},
    {-1069, 947},
    {-1064, 947},
    {-1060, 942},
    {-1055, 938},
    {-1050, 938},
    {-1045, 933},
    {-1040, 928},
    {-1035, 928},
    {-1030, 923},
    {-1025, 918},
    {-1021, 918},
    {-1016, 913},
    {-1011, 908},
    {-1006, 903},
    {-1001, 898},
    {-996, 894},
    {-991, 894},
    {-991, 889},
    {-986, 884},
    {-981, 889},
    {-981, 894},
    {-981, 898},
    {-981, 903},
    {-981, 908},
    {-981, 913},
    {-981, 918},
    {-981, 923},
    {-981, 928},
    {-981, 933},
    {-981, 938},
    {-981, 942},
    {-981, 947},
    {-981, 952},
    {-981, 957},
    {-981, 962},
    {-977, 967},
    {-972, 967},
    {-967, 967},
    {-962, 967},
    {-957, 967},
    {-952, 967},
    {-947, 962},
    {-942, 962},
    {-942, 957},
    {-942, 952},
    {-942, 947},
    {-942, 942},
    {-942, 938},
    {-942, 933},
    {-942, 928},
    {-942, 923},
    {-942, 918},
    {-942, 913},
    {-942, 908},
    {-942, 903},
    {-942, 898},
    {-942, 894},
    {-938, 889},
    {-933, 894},
    {-928, 898},
    {-923, 903},
    {-918, 908},
    {-913, 908},
    {-908, 913},
    {-903, 918},
    {-898, 923},
    {-894, 923},
    {-889, 928},
    {-884, 933},
    {-879, 933},
    {-874, 938},
    {-869, 942},
    {-864, 942},
    {-859, 947},
    {-854, 947},
    {-850, 952},
    {-845, 952},
    {-840, 952},
    {-835, 947},
    {-835, 942},
    {-830, 938},
    {-825, 933},
    {-825, 928},
    {-820, 923},
    {-825, 918},
    {-825, 913},
    {-830, 913},
    {-835, 908},
    {-840, 903},
    {-845, 903},
    {-850, 898},
    {-854, 898},
    {-859, 894},
    {-864, 894},
    {-869, 889},
    {-874, 884},
    {-879, 879},
    {-884, 879},
    {-889, 874},
    {-894, 869},
    {-898, 869},
    {-903, 864},
    {-908, 859},
    {-913, 854},
    {-918, 850},
    {-923, 845},
    {-918, 840},
    {-913, 840},
    {-908, 840},
    {-903, 840},
    {-898, 840},
    {-894, 840},
    {-889, 840},
    {-884, 840},
    {-879, 840},
    {-874, 840},
    {-869, 840},
    {-864, 840},
    {-859, 840},
    {-854, 840},
    {-850, 840},
    {-845, 840},
    {-840, 835},
    {-835, 835},
    {-835, 830},
    {-835, 825},
    {-835, 820},
    {-835, 815},
    {-835, 811},
    {-835, 806},
    {-835, 801},
    {-840, 801},
    {-845, 796},
    {-850, 796},
    {-854, 796},
    {-859, 796},
    {-864, 796},
    {-869, 796},
    {-874, 796},
    {-879, 796},
    {-884, 796},
    {-889, 796},
    {-894, 791},
    {-889, 786},
    {-884, 781},
    {-879, 776},
    {-874, 771},
    {-874, 767},
    {-869, 762},
    {-864, 757},
    {-859, 752},
    {-854, 747},
    {-850, 742},
    {-850, 737},
    {-850, 732},
    {-854, 728},
    {-859, 723},
    {-864, 718},
    {-869, 713},
    {-874, 713},
    {-879, 713},
    {-884, 713},
    {-889, 718},
    {-889, 723},
    {-894, 728},
    {-898, 732},
    {-903, 737},
    {-908, 742},
    {-913, 747},
    {-918, 752},
    {-923, 757},
    {-928, 762},
    {-928, 767},
    {-928, 771},
    {-923, 776},
    {-918, 781},
    {-913, 786},
    {-908, 791},
    {-913, 796},
    {-918, 796},
    {-923, 796},
    {-928, 796},
    {-933, 796},
    {-938, 796},
    {-942, 791},
    {-942, 786},
    {-942, 781},
    {-942, 776},
    {-942, 771},
    {-942, 767},
    {-942, 762},
    {-942, 757},
    {-942, 752},
    {-942, 747},
    {-942, 742},
    {-942, 737},
    {-942, 732},
    {-942, 728},
    {-942, 723},
    {-942, 718},
    {-942, 713},
    {-942, 708},
    {-942, 703},
    {-942, 698},
    {-947, 698},
    {-952, 698},
    {-957, 698},
    {-962, 698},
    {-967, 698},
    {-972, 698},
    {-977, 698},
    {-635, 698},
    {-635, 703},
    {-635, 708},
    {-635, 713},
    {-635, 718},
    {-640, 723},
    {-645, 728},
    {-649, 728},
    {-654, 728},
    {-659, 728},
    {-659, 732},
    {-654, 728},
    {-649, 728},
    {-645, 728},
    {-640, 728},
    {-640, 723},
    {-635, 718},
    {-635, 713},
    {-635, 708},
    {-635, 703},
    {-630, 698},
    {-625, 698},
    {-620, 698},
    {-615, 698},
    {-610, 698},
    {-605, 703},
    {-601, 708},
    {-601, 713},
    {-605, 718},
    {-605, 723},
    {-605, 728},
    {-601, 728},
    {-596, 728},
    {-591, 728},
    {-591, 723},
    {-586, 718},
    {-586, 713},
    {-586, 708},
    {-586, 703},
    {-581, 698},
    {-576, 698},
    {-571, 698},
    {-566, 698},
    {-562, 698},
    {-557, 703},
    {-557, 708},
    {-557, 713},
    {-557, 718},
    {-552, 723},
    {-552, 728},
    {-547, 728},
    {-542, 728},
    {-537, 728},
    {-532, 728},
    {-527, 732},
    {-527, 737},
    {-527, 742},
    {-527, 747},
    {-527, 752},
    {-527, 757},
    {-532, 762},
    {-537, 762},
    {-542, 762},
    {-547, 762},
    {-552, 762},
    {-552, 767},
    {-557, 771},
    {-557, 776},
    {-557, 781},
    {-552, 786},
    {-547, 786},
    {-542, 786},
    {-537, 786},
    {-532, 786},
    {-527, 791},
    {-522, 796},
    {-522, 801},
    {-522, 806},
    {-522, 811},
    {-522, 815},
    {-527, 820},
    {-532, 820},
    {-537, 820},
    {-542, 820},
    {-547, 820},
    {-552, 820},
    {-557, 820},
    {-562, 820},
    {-566, 820},
    {-571, 820},
    {-576, 820},
    {-581, 820},
    {-586, 820},
    {-591, 820},
    {-596, 820},
    {-601, 820},
    {-605, 820},
    {-610, 820},
    {-615, 820},
    {-620, 820},
    {-625, 820},
    {-630, 820},
    {-635, 820},
    {-640, 820},
    {-645, 820},
    {-649, 820},
    {-654, 820},
    {-659, 815},
    {-659, 811},
    {-659, 806},
    {-659, 801},
    {-659, 796},
    {-659, 791},
    {-654, 786},
    {-649, 786},
    {-645, 786},
    {-640, 786},
    {-640, 781},
    {-640, 776},
    {-640, 771},
    {-640, 767},
    {-640, 762},
    {-645, 762},
    {-649, 762},
    {-654, 762},
    {-659, 757},
    {-664, 752},
    {-664, 747},
    {-664, 742},
    {-664, 747},
    {-664, 752},
    {-659, 757},
    {-659, 762},
    {-654, 762},
    {-649, 762},
    {-645, 762},
    {-640, 767},
    {-640, 771},
    {-640, 776},
    {-640, 781},
    {-645, 786},
    {-649, 786},
    {-654, 786},
    {-659, 791},
    {-659, 796},
    {-659, 801},
    {-659, 806},
    {-659, 811},
    {-659, 815},
    {-659, 820},
    {-654, 820},
    {-649, 820},
    {-645, 820},
    {-640, 820},
    {-635, 820},
    {-630, 820},
    {-625, 820},
    {-620, 820},
    {-615, 820},
    {-610, 820},
    {-605, 820},
    {-601, 820},
    {-596, 820},
    {-591, 820},
    {-586, 820},
    {-581, 820},
    {-576, 820},
    {-571, 820},
    {-566, 820},
    {-562, 820},
    {-557, 820},
    {-552, 820},
    {-547, 820},
    {-542, 820},
    {-537, 820},
    {-532, 820},
    {-527, 820},
    {-522, 820},
    {-522, 815},
    {-522, 811},
    {-522, 806},
    {-522, 801},
    {-522, 796},
    {-522, 791},
    {-527, 791},
    {-532, 786},
    {-537, 786},
    {-542, 786},
    {-547, 786},
    {-552, 786},
    {-557, 781},
    {-557, 776},
    {-557, 771},
    {-552, 767},
    {-547, 762},
    {-542, 762},
    {-537, 762},
    {-532, 762},
    {-527, 762},
    {-527, 757},
    {-527, 752},
    {-527, 747},
    {-527, 742},
    {-527, 737},
    {-527, 732},
    {-527, 728},
    {-532, 728},
    {-537, 728},
    {-542, 728},
    {-547, 728},
    {-552, 723},
    {-557, 718},
    {-557, 713},
    {-557, 708},
    {-557, 703},
    {-557, 698},
    {-562, 698},
    {-566, 698},
    {-571, 698},
    {-576, 698},
    {-581, 698},
    {-586, 698},
    {-586, 703},
    {-586, 708},
    {-586, 713},
    {-586, 718},
    {-591, 723},
    {-596, 728},
    {-601, 728},
    {-605, 723},
    {-605, 718},
    {-601, 713},
    {-601, 708},
    {-601, 703},
    {-605, 703},
    {-610, 698},
    {-615, 698},
    {-620, 698},
    {-625, 698},
    {-630, 698},
    {-146, 708},
    {-142, 703},
    {-137, 703},
    {-132, 703},
    {-127, 703},
    {-122, 703},
    {-117, 703},
    {-112, 703},
    {-107, 703},
    {-103, 703},
    {-98, 703},
    {-93, 703},
    {-88, 703},
    {-83, 703},
    {-78, 703},
    {-73, 703},
    {-68, 703},
    {-63, 703},
    {-59, 703},
    {-54, 703},
    {-49, 703},
    {-44, 703},
    {-39, 703},
    {-34, 703},
    {-29, 703},
    {-24, 703},
    {-20, 703},
    {-15, 703},
    {-10, 703},
    {-5, 703},
    {0, 703},
    {5, 703},
    {10, 703},
    {15, 703},
    {20, 703},
    {24, 703},
    {29, 703},
    {34, 703},
    {39, 703},
    {44, 703},
    {49, 703},
    {54, 703},
    {59, 703},
    {63, 703},
    {68, 703},
    {73, 708},
    {73, 713},
    {73, 718},
    {73, 723},
    {73, 728},
    {73, 732},
    {73, 737},
    {73, 742},
    {68, 747},
    {63, 747},
    {59, 752},
    {54, 757},
    {49, 757},
    {44, 762},
    {39, 767},
    {34, 767},
    {29, 771},
    {24, 776},
    {20, 776},
    {15, 781},
    {10, 786},
    {5, 791},
    {0, 791},
    {-5, 796},
    {-10, 801},
    {-15, 801},
    {-15, 806},
    {-15, 811},
    {-15, 815},
    {-10, 815},
    {-5, 815},
    {0, 815},
    {5, 815},
    {10, 815},
    {15, 815},
    {20, 815},
    {24, 815},
    {29, 815},
    {34, 815},
    {39, 815},
    {44, 815},
    {49, 815},
    {54, 815},
    {59, 815},
    {63, 815},
    {68, 815},
    {73, 815},
    {78, 815},
    {83, 815},
    {88, 815},
    {93, 820},
    {93, 825},
    {93, 830},
    {93, 835},
    {93, 840},
    {93, 845},
    {93, 850},
    {88, 854},
    {83, 854},
    {78, 854},
    {73, 854},
    {68, 854},
    {63, 854},
    {59, 854},
    {54, 854},
    {49, 854},
    {44, 854},
    {39, 854},
    {34, 854},
    {29, 854},
    {24, 854},
    {20, 854},
    {15, 854},
    {10, 854},
    {5, 854},
    {0, 854},
    {-5, 854},
    {-10, 854},
    {-15, 854},
    {-15, 859},
    {-15, 864},
    {-15, 869},
    {-15, 874},
    {-15, 879},
    {-15, 884},
    {-15, 889},
    {-15, 894},
    {-15, 898},
    {-15, 903},
    {-15, 908},
    {-15, 913},
    {-15, 918},
    {-15, 923},
    {-15, 928},
    {-20, 933},
    {-20, 938},
    {-20, 942},
    {-20, 947},
    {-24, 952},
    {-29, 957},
    {-34, 962},
    {-39, 962},
    {-44, 962},
    {-49, 967},
    {-54, 967},
    {-59, 967},
    {-63, 967},
    {-68, 967},
    {-73, 967},
    {-78, 967},
    {-83, 967},
    {-88, 967},
    {-93, 962},
    {-98, 957},
    {-98, 952},
    {-98, 947},
    {-103, 942},
    {-103, 938},
    {-98, 933},
    {-93, 928},
    {-88, 928},
    {-83, 928},
    {-78, 928},
    {-73, 928},
    {-68, 928},
    {-63, 928},
    {-59, 923},
    {-59, 918},
    {-59, 913},
    {-59, 908},
    {-59, 903},
    {-59, 898},
    {-59, 894},
    {-59, 889},
    {-59, 884},
    {-59, 879},
    {-59, 874},
    {-59, 869},
    {-59, 864},
    {-59, 859},
    {-63, 854},
    {-68, 854},
    {-73, 854},
    {-78, 854},
    {-83, 854},
    {-88, 854},
    {-93, 854},
    {-98, 854},
    {-103, 854},
    {-107, 854},
    {-112, 854},
    {-117, 854},
    {-122, 854},
    {-127, 854},
    {-132, 854},
    {-137, 854},
    {-142, 854},
    {-146, 854},
    {-151, 854},
    {-156, 854},
    {-161, 854},
    {-166, 854},
    {-171, 850},
    {-171, 845},
    {-171, 840},
    {-171, 835},
    {-171, 830},
    {-171, 825},
    {-171, 820},
    {-166, 815},
    {-161, 815},
    {-156, 815},
    {-151, 815},
    {-146, 815},
    {-142, 815},
    {-137, 815},
    {-132, 815},
    {-127, 815},
    {-122, 815},
    {-117, 815},
    {-112, 815},
    {-107, 815},
    {-103, 815},
    {-98, 815},
    {-93, 815},
    {-88, 815},
    {-83, 815},
    {-78, 815},
    {-73, 815},
    {-68, 815},
    {-63, 815},
    {-59, 811},
    {-59, 806},
    {-59, 801},
    {-59, 796},
    {-59, 791},
    {-59, 786},
    {-54, 781},
    {-49, 776},
    {-44, 776},
    {-39, 771},
    {-34, 771},
    {-29, 767},
    {-24, 767},
    {-20, 762},
    {-15, 762},
    {-10, 757},
    {-5, 752},
    {0, 752},
    {5, 752},
    {0, 752},
    {-5, 752},
    {-10, 747},
    {-5, 742},
    {0, 742},
    {5, 742},
    {0, 742},
    {-5, 742},
    {-10, 747},
    {-15, 747},
    {-20, 747},
    {-24, 747},
    {-29, 747},
    {-34, 747},
    {-39, 747},
    {-44, 747},
    {-49, 747},
    {-54, 747},
    {-59, 747},
    {-63, 747},
    {-68, 747},
    {-73, 747},
    {-78, 747},
    {-83, 747},
    {-88, 747},
    {-93, 747},
    {-98, 747},
    {-103, 747},
    {-107, 747},
    {-112, 747},
    {-117, 747},
    {-122, 747},
    {-127, 747},
    {-132, 747},
    {-137, 747},
    {-142, 747},
    {-146, 742},
    {-151, 737},
    {-151, 732},
    {-151, 728},
    {-151, 723},
    {-151, 718},
    {-151, 713},
    {-142, 703},
    {-146, 708},
    {-151, 708},
    {-151, 713},
    {-151, 718},
    {-151, 723},
    {-151, 728},
    {-151, 732},
    {-151, 737},
    {-151, 742},
    {-146, 742},
    {-142, 747},
    {-137, 747},
    {-132, 747},
    {-127, 747},
    {-122, 747},
    {-117, 747},
    {-112, 747},
    {-107, 747},
    {-103, 747},
    {-98, 747},
    {-93, 747},
    {-88, 747},
    {-83, 747},
    {-78, 747},
    {-73, 747},
    {-68, 747},
    {-63, 747},
    {-59, 747},
    {-54, 747},
    {-49, 747},
    {-44, 747},
    {-39, 747},
    {-34, 747},
    {-29, 747},
    {-24, 747},
    {-20, 747},
    {-15, 747},
    {-10, 747},
    {-5, 752},
    {-10, 757},
    {-15, 757},
    {-20, 762},
    {-24, 762},
    {-29, 767},
    {-34, 767},
    {-39, 771},
    {-44, 771},
    {-49, 776},
    {-54, 776},
    {-59, 781},
    {-59, 786},
    {-59, 791},
    {-59, 796},
    {-59, 801},
    {-59, 806},
    {-63, 811},
    {-68, 815},
    {-73, 815},
    {-78, 815},
    {-83, 815},
    {-88, 815},
    {-93, 815},
    {-98, 815},
    {-103, 815},
    {-107, 815},
    {-112, 815},
    {-117, 815},
    {-122, 815},
    {-127, 815},
    {-132, 815},
    {-137, 815},
    {-142, 815},
    {-146, 815},
    {-151, 815},
    {-156, 815},
    {-161, 815},
    {-166, 815},
    {-171, 815},
    {-171, 820},
    {-171, 825},
    {-171, 830},
    {-171, 835},
    {-171, 840},
    {-171, 845},
    {-171, 850},
    {-171, 854},
    {-166, 854},
    {-161, 854},
    {-156, 854},
    {-151, 854},
    {-146, 854},
    {-142, 854},
    {-137, 854},
    {-132, 854},
    {-127, 854},
    {-122, 854},
    {-117, 854},
    {-112, 854},
    {-107, 854},
    {-103, 854},
    {-98, 854},
    {-93, 854},
    {-88, 854},
    {-83, 854},
    {-78, 854},
    {-73, 854},
    {-68, 854},
    {-63, 859},
    {-59, 864},
    {-59, 869},
    {-59, 874},
    {-59, 879},
    {-59, 884},
    {-59, 889},
    {-59, 894},
    {-59, 898},
    {-59, 903},
    {-59, 908},
    {-59, 913},
    {-59, 918},
    {-63, 923},
    {-68, 928},
    {-73, 928},
    {-78, 928},
    {-83, 928},
    {-88, 928},
    {-93, 928},
    {-98, 933},
    {-103, 933},
    {-103, 938},
    {-103, 942},
    {-98, 947},
    {-98, 952},
    {-98, 957},
    {-93, 962},
    {-93, 967},
    {-88, 967},
    {-83, 967},
    {-78, 967},
    {-73, 967},
    {-68, 967},
    {-63, 967},
    {-59, 967},
    {-54, 967},
    {-49, 967},
    {-44, 962},
    {-39, 962},
    {-34, 962},
    {-29, 962},
    {-24, 957},
    {-24, 952},
    {-20, 947},
    {-20, 942},
    {-20, 938},
    {-20, 933},
    {-15, 928},
    {-15, 923},
    {-15, 918},
    {-15, 913},
    {-15, 908},
    {-15, 903},
    {-15, 898},
    {-15, 894},
    {-15, 889},
    {-15, 884},
    {-15, 879},
    {-15, 874},
    {-15, 869},
    {-15, 864},
    {-15, 859},
    {-10, 854},
    {-5, 854},
    {0, 854},
    {5, 854},
    {10, 854},
    {15, 854},
    {20, 854},
    {24, 854},
    {29, 854},
    {34, 854},
    {39, 854},
    {44, 854},
    {49, 854},
    {54, 854},
    {59, 854},
    {63, 854},
    {68, 854},
    {73, 854},
    {78, 854},
    {83, 854},
    {88, 854},
    {93, 854},
    {93, 850},
    {93, 845},
    {93, 840},
    {93, 835},
    {93, 830},
    {93, 825},
    {93, 820},
    {93, 815},
    {88, 815},
    {83, 815},
    {78, 815},
    {73, 815},
    {68, 815},
    {63, 815},
    {59, 815},
    {54, 815},
    {49, 815},
    {44, 815},
    {39, 815},
    {34, 815},
    {29, 815},
    {24, 815},
    {20, 815},
    {15, 815},
    {10, 815},
    {5, 815},
    {0, 815},
    {-5, 815},
    {-10, 815},
    {-15, 811},
    {-15, 806},
    {-10, 801},
    {-5, 801},
    {0, 796},
    {5, 791},
    {10, 791},
    {15, 786},
    {20, 781},
    {24, 776},
    {29, 776},
    {34, 771},
    {39, 767},
    {44, 767},
    {49, 762},
    {54, 757},
    {59, 757},
    {63, 752},
    {68, 747},
    {73, 747},
    {73, 742},
    {73, 737},
    {73, 732},
    {73, 728},
    {73, 723},
    {73, 718},
    {73, 713},
    {73, 708},
    {68, 703},
    {63, 703},
    {59, 703},
    {54, 703},
    {49, 703},
    {44, 703},
    {39, 703},
    {34, 703},
    {29, 703},
    {24, 703},
    {20, 703},
    {15, 703},
    {10, 703},
    {5, 703},
    {0, 703},
    {-5, 703},
    {-10, 703},
    {-15, 703},
    {-20, 703},
    {-24, 703},
    {-29, 703},
    {-34, 703},
    {-39, 703},
    {-44, 703},
    {-49, 703},
    {-54, 703},
    {-59, 703},
    {-63, 703},
    {-68, 703},
    {-73, 703},
    {-78, 703},
    {-83, 703},
    {-88, 703},
    {-93, 703},
    {-98, 703},
    {-103, 703},
    {-107, 703},
    {-112, 703},
    {-117, 703},
    {-122, 703},
    {-127, 703},
    {-132, 703},
    {-137, 703},
    {161, 718},
    {166, 713},
    {171, 713},
    {176, 713},
    {181, 713},
    {186, 713},
    {190, 713},
    {195, 713},
    {200, 713},
    {205, 713},
    {210, 713},
    {215, 713},
    {220, 713},
    {225, 713},
    {229, 713},
    {234, 713},
    {239, 713},
    {244, 713},
    {249, 713},
    {254, 713},
    {259, 713},
    {264, 713},
    {269, 713},
    {273, 713},
    {278, 713},
    {283, 713},
    {288, 713},
    {293, 713},
    {298, 713},
    {303, 713},
    {308, 713},
    {312, 713},
    {317, 713},
    {322, 713},
    {327, 713},
    {332, 713},
    {337, 713},
    {342, 713},
    {347, 713},
    {352, 713},
    {356, 713},
    {361, 713},
    {366, 713},
    {371, 713},
    {376, 713},
    {381, 713},
    {386, 718},
    {391, 723},
    {391, 728},
    {391, 732},
    {391, 737},
    {391, 742},
    {391, 747},
    {386, 752},
    {381, 757},
    {376, 757},
    {371, 757},
    {366, 757},
    {361, 757},
    {356, 757},
    {352, 757},
    {347, 757},
    {342, 757},
    {337, 757},
    {332, 757},
    {327, 757},
    {322, 757},
    {317, 757},
    {312, 757},
    {308, 757},
    {303, 757},
    {298, 757},
    {298, 762},
    {298, 767},
    {298, 771},
    {298, 776},
    {298, 781},
    {298, 786},
    {298, 791},
    {298, 796},
    {298, 801},
    {298, 806},
    {298, 811},
    {298, 815},
    {298, 820},
    {298, 825},
    {298, 830},
    {298, 835},
    {298, 840},
    {298, 845},
    {298, 850},
    {298, 854},
    {298, 859},
    {298, 864},
    {298, 869},
    {298, 874},
    {298, 879},
    {298, 884},
    {298, 889},
    {298, 894},
    {298, 898},
    {298, 903},
    {298, 908},
    {298, 913},
    {303, 913},
    {308, 913},
    {312, 913},
    {317, 913},
    {322, 913},
    {327, 913},
    {332, 913},
    {337, 913},
    {342, 913},
    {347, 913},
    {352, 913},
    {356, 913},
    {361, 913},
    {366, 913},
    {371, 913},
    {376, 913},
    {381, 913},
    {386, 913},
    {391, 913},
    {396, 913},
    {400, 913},
    {405, 918},
    {410, 923},
    {410, 928},
    {410, 933},
    {410, 938},
    {410, 942},
    {410, 947},
    {405, 952},
    {400, 957},
    {396, 957},
    {391, 957},
    {386, 957},
    {381, 957},
    {376, 957},
    {371, 957},
    {366, 957},
    {361, 957},
    {356, 957},
    {352, 957},
    {347, 957},
    {342, 957},
    {337, 957},
    {332, 957},
    {327, 957},
    {322, 957},
    {317, 957},
    {312, 957},
    {308, 957},
    {303, 957},
    {298, 957},
    {293, 957},
    {288, 957},
    {283, 957},
    {278, 957},
    {273, 957},
    {269, 957},
    {264, 957},
    {259, 957},
    {254, 957},
    {249, 957},
    {244, 957},
    {239, 957},
    {234, 957},
    {229, 957},
    {225, 957},
    {220, 957},
    {215, 957},
    {210, 957},
    {205, 957},
    {200, 957},
    {195, 957},
    {190, 957},
    {186, 957},
    {181, 957},
    {176, 957},
    {171, 957},
    {166, 957},
    {161, 957},
    {156, 957},
    {151, 957},
    {146, 952},
    {142, 947},
    {142, 942},
    {142, 938},
    {142, 933},
    {142, 928},
    {142, 923},
    {146, 918},
    {151, 913},
    {156, 913},
    {161, 913},
    {166, 913},
    {171, 913},
    {176, 913},
    {181, 913},
    {186, 913},
    {190, 913},
    {195, 913},
    {200, 913},
    {205, 913},
    {210, 913},
    {215, 913},
    {220, 913},
    {225, 913},
    {229, 913},
    {234, 913},
    {239, 913},
    {244, 913},
    {249, 913},
    {254, 913},
    {254, 908},
    {254, 903},
    {254, 898},
    {254, 894},
    {254, 889},
    {254, 884},
    {254, 879},
    {254, 874},
    {254, 869},
    {254, 864},
    {254, 859},
    {254, 854},
    {254, 850},
    {254, 845},
    {254, 840},
    {254, 835},
    {254, 830},
    {254, 825},
    {254, 820},
    {254, 815},
    {254, 811},
    {254, 806},
    {254, 801},
    {254, 796},
    {254, 791},
    {254, 786},
    {254, 781},
    {254, 776},
    {254, 771},
    {254, 767},
    {254, 762},
    {254, 757},
    {249, 757},
    {244, 757},
    {239, 757},
    {234, 757},
    {229, 757},
    {225, 757},
    {220, 757},
    {215, 757},
    {210, 757},
    {205, 757},
    {200, 757},
    {195, 757},
    {190, 757},
    {186, 757},
    {181, 757},
    {176, 757},
    {171, 757},
    {166, 757},
    {161, 752},
    {161, 747},
    {161, 742},
    {161, 737},
    {161, 732},
    {161, 728},
    {161, 723},
    {166, 713},
    {161, 718},
    {161, 723},
    {161, 728},
    {161, 732},
    {161, 737},
    {161, 742},
    {161, 747},
    {161, 752},
    {166, 757},
    {171, 757},
    {176, 757},
    {181, 757},
    {186, 757},
    {190, 757},
    {195, 757},
    {200, 757},
    {205, 757},
    {210, 757},
    {215, 757},
    {220, 757},
    {225, 757},
    {229, 757},
    {234, 757},
    {239, 757},
    {244, 757},
    {249, 757},
    {254, 762},
    {254, 767},
    {254, 771},
    {254, 776},
    {254, 781},
    {254, 786},
    {254, 791},
    {254, 796},
    {254, 801},
    {254, 806},
    {254, 811},
    {254, 815},
    {254, 820},
    {254, 825},
    {254, 830},
    {254, 835},
    {254, 840},
    {254, 845},
    {254, 850},
    {254, 854},
    {254, 859},
    {254, 864},
    {254, 869},
    {254, 874},
    {254, 879},
    {254, 884},
    {254, 889},
    {254, 894},
    {254, 898},
    {254, 903},
    {254, 908},
    {249, 913},
    {244, 913},
    {239, 913},
    {234, 913},
    {229, 913},
    {225, 913},
    {220, 913},
    {215, 913},
    {210, 913},
    {205, 913},
    {200, 913},
    {195, 913},
    {190, 913},
    {186, 913},
    {181, 913},
    {176, 913},
    {171, 913},
    {166, 913},
    {161, 913},
    {156, 913},
    {151, 913},
    {146, 918},
    {142, 918},
    {142, 923},
    {142, 928},
    {142, 933},
    {142, 938},
    {142, 942},
    {142, 947},
    {142, 952},
    {146, 952},
    {151, 957},
    {156, 957},
    {161, 957},
    {166, 957},
    {171, 957},
    {176, 957},
    {181, 957},
    {186, 957},
    {190, 957},
    {195, 957},
    {200, 957},
    {205, 957},
    {210, 957},
    {215, 957},
    {220, 957},
    {225, 957},
    {229, 957},
    {234, 957},
    {239, 957},
    {244, 957},
    {249, 957},
    {254, 957},
    {259, 957},
    {264, 957},
    {269, 957},
    {273, 957},
    {278, 957},
    {283, 957},
    {288, 957},
    {293, 957},
    {298, 957},
    {303, 957},
    {308, 957},
    {312, 957},
    {317, 957},
    {322, 957},
    {327, 957},
    {332, 957},
    {337, 957},
    {342, 957},
    {347, 957},
    {352, 957},
    {356, 957},
    {361, 957},
    {366, 957},
    {371, 957},
    {376, 957},
    {381, 957},
    {386, 957},
    {391, 957},
    {396, 957},
    {400, 957},
    {405, 952},
    {410, 952},
    {410, 947},
    {410, 942},
    {410, 938},
    {410, 933},
    {410, 928},
    {410, 923},
    {410, 918},
    {405, 918},
    {400, 913},
    {396, 913},
    {391, 913},
    {386, 913},
    {381, 913},
    {376, 913},
    {371, 913},
    {366, 913},
    {361, 913},
    {356, 913},
    {352, 913},
    {347, 913},
    {342, 913},
    {337, 913},
    {332, 913},
    {327, 913},
    {322, 913},
    {317, 913},
    {312, 913},
    {308, 913},
    {303, 913},
    {298, 908},
    {298, 903},
    {298, 898},
    {298, 894},
    {298, 889},
    {298, 884},
    {298, 879},
    {298, 874},
    {298, 869},
    {298, 864},
    {298, 859},
    {298, 854},
    {298, 850},
    {298, 845},
    {298, 840},
    {298, 835},
    {298, 830},
    {298, 825},
    {298, 820},
    {298, 815},
    {298, 811},
    {298, 806},
    {298, 801},
    {298, 796},
    {298, 791},
    {298, 786},
    {298, 781},
    {298, 776},
    {298, 771},
    {298, 767},
    {298, 762},
    {303, 757},
    {308, 757},
    {312, 757},
    {317, 757},
    {322, 757},
    {327, 757},
    {332, 757},
    {337, 757},
    {342, 757},
    {347, 757},
    {352, 757},
    {356, 757},
    {361, 757},
    {366, 757},
    {371, 757},
    {376, 757},
    {381, 757},
    {386, 752},
    {391, 752},
    {391, 747},
    {391, 742},
    {391, 737},
    {391, 732},
    {391, 728},
    {391, 723},
    {391, 718},
    {386, 718},
    {381, 713},
    {376, 713},
    {371, 713},
    {366, 713},
    {361, 713},
    {356, 713},
    {352, 713},
    {347, 713},
    {342, 713},
    {337, 713},
    {332, 713},
    {327, 713},
    {322, 713},
    {317, 713},
    {312, 713},
    {308, 713},
    {303, 713},
    {298, 713},
    {293, 713},
    {288, 713},
    {283, 713},
    {278, 713},
    {273, 713},
    {269, 713},
    {264, 713},
    {259, 713},
    {254, 713},
    {249, 713},
    {244, 713},
    {239, 713},
    {234, 713},
    {229, 713},
    {225, 713},
    {220, 713},
    {215, 713},
    {210, 713},
    {205, 713},
    {200, 713},
    {195, 713},
    {190, 713},
    {186, 713},
    {181, 713},
    {176, 713},
    {171, 713},
    {508, 693},
    {508, 698},
    {508, 703},
    {508, 708},
    {503, 713},
    {503, 718},
    {503, 723},
    {498, 728},
    {498, 732},
    {498, 737},
    {493, 742},
    {493, 747},
    {488, 752},
    {488, 757},
    {483, 762},
    {483, 767},
    {479, 771},
    {479, 776},
    {474, 781},
    {469, 786},
    {469, 791},
    {464, 796},
    {459, 801},
    {459, 806},
    {454, 811},
    {449, 815},
    {449, 820},
    {449, 825},
    {449, 830},
    {449, 835},
    {454, 840},
    {459, 845},
    {464, 850},
    {464, 854},
    {469, 854},
    {474, 854},
    {479, 854},
    {479, 850},
    {479, 845},
    {479, 850},
    {474, 854},
    {469, 854},
    {464, 850},
    {464, 845},
    {459, 840},
    {454, 835},
    {449, 830},
    {449, 825},
    {449, 820},
    {454, 815},
    {459, 811},
    {459, 806},
    {464, 801},
    {469, 796},
    {469, 791},
    {474, 786},
    {479, 781},
    {479, 776},
    {483, 771},
    {483, 767},
    {488, 762},
    {488, 757},
    {488, 752},
    {493, 747},
    {493, 742},
    {498, 737},
    {498, 732},
    {498, 728},
    {503, 723},
    {503, 718},
    {503, 713},
    {508, 708},
    {508, 703},
    {508, 698},
    {513, 693},
    {518, 693},
    {522, 693},
    {527, 698},
    {532, 698},
    {537, 698},
    {542, 703},
    {547, 708},
    {542, 713},
    {542, 718},
    {542, 723},
    {542, 728},
    {537, 732},
    {537, 737},
    {537, 742},
    {532, 747},
    {532, 752},
    {527, 757},
    {527, 762},
    {527, 767},
    {527, 771},
    {527, 776},
    {527, 781},
    {527, 786},
    {527, 791},
    {532, 796},
    {532, 801},
    {532, 796},
    {537, 791},
    {542, 786},
    {547, 781},
    {547, 776},
    {552, 771},
    {552, 767},
    {557, 762},
    {562, 757},
    {562, 752},
    {562, 747},
    {566, 742},
    {566, 737},
    {571, 732},
    {571, 728},
    {571, 723},
    {576, 718},
    {576, 713},
    {576, 708},
    {581, 703},
    {581, 698},
    {586, 693},
    {591, 693},
    {596, 693},
    {601, 693},
    {605, 693},
    {610, 698},
    {615, 698},
    {620, 703},
    {620, 708},
    {615, 713},
    {615, 718},
    {615, 723},
    {615, 728},
    {620, 728},
    {625, 732},
    {630, 732},
    {635, 732},
    {640, 732},
    {645, 732},
    {649, 732},
    {654, 732},
    {659, 732},
    {664, 732},
    {669, 732},
    {674, 732},
    {679, 732},
    {684, 732},
    {688, 732},
    {693, 732},
    {698, 732},
    {703, 732},
    {708, 732},
    {713, 732},
    {718, 732},
    {723, 737},
    {723, 742},
    {723, 747},
    {723, 752},
    {723, 757},
    {723, 762},
    {718, 767},
    {713, 767},
    {708, 767},
    {703, 767},
    {698, 767},
    {693, 767},
    {688, 767},
    {684, 767},
    {679, 767},
    {674, 767},
    {669, 767},
    {664, 767},
    {659, 767},
    {654, 767},
    {649, 767},
    {645, 767},
    {640, 771},
    {635, 771},
    {635, 776},
    {635, 781},
    {635, 786},
    {635, 791},
    {635, 796},
    {640, 796},
    {645, 801},
    {649, 801},
    {654, 801},
    {659, 801},
    {664, 801},
    {669, 801},
    {674, 801},
    {679, 801},
    {684, 801},
    {688, 801},
    {693, 801},
    {698, 801},
    {703, 801},
    {708, 806},
    {708, 811},
    {708, 815},
    {708, 820},
    {708, 825},
    {708, 830},
    {703, 835},
    {698, 840},
    {693, 840},
    {688, 840},
    {684, 840},
    {679, 840},
    {674, 840},
    {669, 840},
    {664, 840},
    {659, 840},
    {654, 840},
    {649, 840},
    {645, 840},
    {640, 840},
    {635, 840},
    {635, 845},
    {635, 850},
    {635, 854},
    {635, 859},
    {635, 864},
    {635, 869},
    {640, 869},
    {645, 869},
    {649, 869},
    {654, 869},
    {659, 869},
    {664, 869},
    {669, 869},
    {674, 869},
    {679, 869},
    {684, 869},
    {688, 869},
    {693, 869},
    {698, 869},
    {703, 869},
    {708, 874},
    {713, 879},
    {713, 884},
    {713, 889},
    {713, 894},
    {713, 898},
    {708, 903},
    {703, 908},
    {698, 908},
    {693, 908},
    {688, 908},
    {684, 908},
    {679, 908},
    {674, 908},
    {669, 908},
    {664, 908},
    {659, 908},
    {654, 908},
    {649, 908},
    {645, 908},
    {640, 908},
    {635, 908},
    {635, 913},
    {635, 918},
    {635, 923},
    {635, 928},
    {635, 933},
    {635, 938},
    {635, 942},
    {635, 947},
    {635, 952},
    {635, 957},
    {635, 962},
    {630, 967},
    {625, 967},
    {620, 967},
    {615, 967},
    {610, 967},
    {605, 967},
    {601, 967},
    {596, 962},
    {596, 957},
    {596, 952},
    {596, 947},
    {596, 942},
    {596, 938},
    {596, 933},
    {596, 928},
    {596, 923},
    {596, 918},
    {596, 913},
    {596, 908},
    {596, 903},
    {596, 898},
    {596, 894},
    {596, 889},
    {596, 884},
    {596, 879},
    {596, 874},
    {596, 869},
    {596, 864},
    {596, 859},
    {596, 854},
    {596, 850},
    {596, 845},
    {596, 840},
    {596, 835},
    {596, 830},
    {596, 825},
    {596, 820},
    {596, 815},
    {596, 811},
    {596, 806},
    {596, 801},
    {596, 796},
    {596, 791},
    {596, 786},
    {596, 791},
    {596, 796},
    {596, 801},
    {596, 806},
    {596, 811},
    {596, 815},
    {596, 820},
    {596, 825},
    {596, 830},
    {596, 835},
    {596, 840},
    {596, 845},
    {596, 850},
    {596, 854},
    {596, 859},
    {596, 864},
    {596, 869},
    {596, 874},
    {596, 879},
    {596, 884},
    {596, 889},
    {596, 894},
    {596, 898},
    {596, 903},
    {596, 908},
    {596, 913},
    {596, 918},
    {596, 923},
    {596, 928},
    {596, 933},
    {596, 938},
    {596, 942},
    {596, 947},
    {596, 952},
    {596, 957},
    {596, 962},
    {596, 967},
    {601, 967},
    {605, 967},
    {610, 967},
    {615, 967},
    {620, 967},
    {625, 967},
    {630, 967},
    {635, 967},
    {635, 962},
    {635, 957},
    {635, 952},
    {635, 947},
    {635, 942},
    {635, 938},
    {635, 933},
    {635, 928},
    {635, 923},
    {635, 918},
    {635, 913},
    {640, 908},
    {645, 908},
    {649, 908},
    {654, 908},
    {659, 908},
    {664, 908},
    {669, 908},
    {674, 908},
    {679, 908},
    {684, 908},
    {688, 908},
    {693, 908},
    {698, 908},
    {703, 908},
    {708, 908},
    {713, 903},
    {713, 898},
    {713, 894},
    {713, 889},
    {713, 884},
    {713, 879},
    {713, 874},
    {708, 874},
    {703, 869},
    {698, 869},
    {693, 869},
    {688, 869},
    {684, 869},
    {679, 869},
    {674, 869},
    {669, 869},
    {664, 869},
    {659, 869},
    {654, 869},
    {649, 869},
    {645, 869},
    {640, 869},
    {635, 864},
    {635, 859},
    {635, 854},
    {635, 850},
    {635, 845},
    {640, 840},
    {645, 840},
    {649, 840},
    {654, 840},
    {659, 840},
    {664, 840},
    {669, 840},
    {674, 840},
    {679, 840},
    {684, 840},
    {688, 840},
    {693, 840},
    {698, 840},
    {703, 835},
    {708, 835},
    {708, 830},
    {708, 825},
    {708, 820},
    {708, 815},
    {708, 811},
    {708, 806},
    {708, 801},
    {703, 801},
    {698, 801},
    {693, 801},
    {688, 801},
    {684, 801},
    {679, 801},
    {674, 801},
    {669, 801},
    {664, 801},
    {659, 801},
    {654, 801},
    {649, 801},
    {645, 801},
    {640, 796},
    {635, 791},
    {635, 786},
    {635, 781},
    {635, 776},
    {640, 771},
    {645, 767},
    {649, 767},
    {654, 767},
    {659, 767},
    {664, 767},
    {669, 767},
    {674, 767},
    {679, 767},
    {684, 767},
    {688, 767},
    {693, 767},
    {698, 767},
    {703, 767},
    {708, 767},
    {713, 767},
    {718, 767},
    {723, 767},
    {723, 762},
    {723, 757},
    {723, 752},
    {723, 747},
    {723, 742},
    {723, 737},
    {723, 732},
    {718, 732},
    {713, 732},
    {708, 732},
    {703, 732},
    {698, 732},
    {693, 732},
    {688, 732},
    {684, 732},
    {679, 732},
    {674, 732},
    {669, 732},
    {664, 732},
    {659, 732},
    {654, 732},
    {649, 732},
    {645, 732},
    {640, 732},
    {635, 732},
    {630, 732},
    {625, 732},
    {620, 728},
    {615, 723},
    {615, 718},
    {615, 713},
    {620, 708},
    {620, 703},
    {620, 698},
    {615, 698},
    {610, 698},
    {605, 693},
    {601, 693},
    {596, 693},
    {591, 693},
    {586, 693},
    {581, 693},
    {581, 698},
    {581, 703},
    {576, 708},
    {576, 713},
    {576, 718},
    {571, 723},
    {571, 728},
    {571, 732},
    {566, 737},
    {566, 742},
    {562, 747},
    {562, 752},
    {557, 757},
    {552, 762},
    {552, 767},
    {547, 771},
    {547, 776},
    {542, 781},
    {537, 786},
    {537, 791},
    {532, 796},
    {527, 791},
    {527, 786},
    {527, 781},
    {527, 776},
    {527, 771},
    {527, 767},
    {527, 762},
    {527, 757},
    {532, 752},
    {532, 747},
    {537, 742},
    {537, 737},
    {537, 732},
    {542, 728},
    {542, 723},
    {542, 718},
    {542, 713},
    {547, 708},
    {547, 703},
    {542, 698},
    {537, 698},
    {532, 698},
    {527, 698},
    {522, 693},
    {518, 693},
    {513, 693},
    {586, 781},
    {586, 786},
    {581, 791},
    {581, 796},
    {576, 801},
    {571, 806},
    {571, 811},
    {566, 815},
    {562, 820},
    {562, 825},
    {557, 830},
    {552, 830},
    {547, 825},
    {542, 825},
    {537, 820},
    {532, 815},
    {527, 815},
    {527, 820},
    {527, 825},
    {527, 830},
    {527, 835},
    {527, 840},
    {527, 845},
    {527, 850},
    {527, 854},
    {527, 859},
    {527, 864},
    {527, 869},
    {527, 874},
    {527, 879},
    {527, 884},
    {527, 889},
    {527, 894},
    {527, 898},
    {527, 903},
    {527, 908},
    {527, 913},
    {527, 918},
    {527, 923},
    {527, 928},
    {527, 933},
    {527, 938},
    {527, 942},
    {527, 947},
    {527, 952},
    {527, 957},
    {522, 962},
    {518, 967},
    {513, 967},
    {508, 967},
    {503, 967},
    {498, 967},
    {493, 967},
    {488, 962},
    {488, 957},
    {488, 952},
    {488, 947},
    {488, 942},
    {488, 938},
    {488, 933},
    {488, 928},
    {488, 923},
    {488, 918},
    {488, 913},
    {488, 908},
    {488, 903},
    {488, 898},
    {488, 894},
    {488, 889},
    {488, 884},
    {488, 879},
    {488, 874},
    {488, 869},
    {488, 864},
    {488, 859},
    {488, 854},
    {488, 850},
    {488, 845},
    {488, 850},
    {488, 854},
    {488, 859},
    {488, 864},
    {488, 869},
    {488, 874},
    {488, 879},
    {488, 884},
    {488, 889},
    {488, 894},
    {488, 898},
    {488, 903},
    {488, 908},
    {488, 913},
    {488, 918},
    {488, 923},
    {488, 928},
    {488, 933},
    {488, 938},
    {488, 942},
    {488, 947},
    {488, 952},
    {488, 957},
    {488, 962},
    {488, 967},
    {493, 967},
    {498, 967},
    {503, 967},
    {508, 967},
    {513, 967},
    {518, 967},
    {522, 967},
    {522, 962},
    {527, 957},
    {527, 952},
    {527, 947},
    {527, 942},
    {527, 938},
    {527, 933},
    {527, 928},
    {527, 923},
    {527, 918},
    {527, 913},
    {527, 908},
    {527, 903},
    {527, 898},
    {527, 894},
    {527, 889},
    {527, 884},
    {527, 879},
    {527, 874},
    {527, 869},
    {527, 864},
    {527, 859},
    {527, 854},
    {527, 850},
    {527, 845},
    {527, 840},
    {527, 835},
    {527, 830},
    {527, 825},
    {527, 820},
    {532, 815},
    {537, 820},
    {537, 825},
    {542, 825},
    {547, 830},
    {552, 830},
    {557, 830},
    {562, 830},
    {562, 825},
    {566, 820},
    {571, 815},
    {571, 811},
    {576, 806},
    {581, 801},
    {581, 796},
    {586, 791},
    {586, 786},
    {811, 757},
    {815, 752},
    {820, 752},
    {825, 752},
    {830, 752},
    {835, 752},
    {840, 752},
    {845, 752},
    {850, 752},
    {854, 752},
    {859, 752},
    {864, 752},
    {869, 752},
    {874, 752},
    {879, 752},
    {884, 752},
    {889, 752},
    {894, 752},
    {898, 752},
    {903, 752},
    {908, 752},
    {913, 752},
    {918, 752},
    {923, 752},
    {928, 752},
    {933, 752},
    {938, 752},
    {942, 752},
    {947, 752},
    {952, 752},
    {957, 752},
    {962, 752},
    {967, 752},
    {972, 752},
    {977, 752},
    {981, 752},
    {986, 752},
    {991, 752},
    {996, 752},
    {1001, 757},
    {1001, 762},
    {996, 767},
    {991, 767},
    {986, 767},
    {981, 767},
    {977, 767},
    {972, 767},
    {967, 767},
    {962, 767},
    {957, 767},
    {952, 767},
    {947, 767},
    {942, 767},
    {938, 767},
    {933, 767},
    {928, 767},
    {923, 767},
    {918, 767},
    {913, 767},
    {908, 767},
    {903, 767},
    {898, 767},
    {894, 767},
    {889, 767},
    {884, 767},
    {879, 767},
    {874, 767},
    {869, 767},
    {864, 767},
    {859, 767},
    {854, 767},
    {850, 767},
    {845, 767},
    {840, 767},
    {835, 767},
    {830, 767},
    {825, 767},
    {820, 767},
    {815, 767},
    {811, 762},
    {811, 752},
    {811, 757},
    {811, 762},
    {811, 767},
    {815, 767},
    {820, 767},
    {825, 767},
    {830, 767},
    {835, 767},
    {840, 767},
    {845, 767},
    {850, 767},
    {854, 767},
    {859, 767},
    {864, 767},
    {869, 767},
    {874, 767},
    {879, 767},
    {884, 767},
    {889, 767},
    {894, 767},
    {898, 767},
    {903, 767},
    {908, 767},
    {913, 767},
    {918, 767},
    {923, 767},
    {928, 767},
    {933, 767},
    {938, 767},
    {942, 767},
    {947, 767},
    {952, 767},
    {957, 767},
    {962, 767},
    {967, 767},
    {972, 767},
    {977, 767},
    {981, 767},
    {986, 767},
    {991, 767},
    {996, 767},
    {1001, 767},
    {1001, 762},
    {1001, 757},
    {1001, 752},
    {996, 752},
    {991, 752},
    {986, 752},
    {981, 752},
    {977, 752},
    {972, 752},
    {967, 752},
    {962, 752},
    {957, 752},
    {952, 752},
    {947, 752},
    {942, 752},
    {938, 752},
    {933, 752},
    {928, 752},
    {923, 752},
    {918, 752},
    {913, 752},
    {908, 752},
    {903, 752},
    {898, 752},
    {894, 752},
    {889, 752},
    {884, 752},
    {879, 752},
    {874, 752},
    {869, 752},
    {864, 752},
    {859, 752},
    {854, 752},
    {850, 752},
    {845, 752},
    {840, 752},
    {835, 752},
    {830, 752},
    {825, 752},
    {820, 752},
    {815, 752},
    {884, 693},
    {884, 698},
    {884, 703},
    {884, 708},
    {884, 713},
    {879, 718},
    {874, 718},
    {869, 718},
    {864, 718},
    {859, 718},
    {854, 718},
    {850, 718},
    {845, 718},
    {840, 718},
    {835, 718},
    {830, 718},
    {825, 718},
    {820, 718},
    {815, 718},
    {811, 718},
    {806, 718},
    {801, 718},
    {796, 718},
    {791, 718},
    {786, 718},
    {781, 718},
    {781, 723},
    {776, 728},
    {776, 732},
    {776, 737},
    {776, 742},
    {776, 747},
    {776, 752},
    {776, 757},
    {776, 762},
    {776, 767},
    {781, 771},
    {781, 776},
    {786, 776},
    {791, 776},
    {796, 776},
    {801, 776},
    {806, 781},
    {806, 786},
    {806, 791},
    {806, 796},
    {811, 801},
    {815, 801},
    {820, 801},
    {825, 801},
    {830, 801},
    {835, 801},
    {840, 801},
    {845, 801},
    {840, 801},
    {835, 801},
    {830, 801},
    {825, 801},
    {820, 801},
    {815, 801},
    {811, 801},
    {806, 796},
    {806, 791},
    {806, 786},
    {806, 781},
    {806, 776},
    {801, 776},
    {796, 776},
    {791, 776},
    {786, 776},
    {781, 771},
    {776, 767},
    {776, 762},
    {776, 757},
    {776, 752},
    {776, 747},
    {776, 742},
    {776, 737},
    {776, 732},
    {776, 728},
    {781, 723},
    {786, 718},
    {791, 718},
    {796, 718},
    {801, 718},
    {806, 718},
    {811, 718},
    {815, 718},
    {820, 718},
    {825, 718},
    {830, 718},
    {835, 718},
    {840, 718},
    {845, 718},
    {850, 718},
    {854, 718},
    {859, 718},
    {864, 718},
    {869, 718},
    {874, 718},
    {879, 718},
    {884, 718},
    {884, 713},
    {884, 708},
    {884, 703},
    {884, 698},
    {889, 693},
    {894, 693},
    {898, 693},
    {903, 693},
    {908, 693},
    {913, 693},
    {918, 693},
    {923, 698},
    {928, 703},
    {928, 708},
    {928, 713},
    {933, 718},
    {938, 718},
    {942, 718},
    {947, 718},
    {952, 718},
    {957, 718},
    {962, 718},
    {967, 718},
    {972, 718},
    {977, 718},
    {981, 718},
    {986, 718},
    {991, 718},
    {996, 718},
    {1001, 718},
    {1006, 718},
    {1011, 718},
    {1016, 718},
    {1021, 718},
    {1025, 718},
    {1030, 723},
    {1035, 728},
    {1035, 732},
    {1035, 737},
    {1035, 742},
    {1035, 747},
    {1035, 752},
    {1035, 757},
    {1035, 762},
    {1035, 767},
    {1030, 771},
    {1025, 776},
    {1021, 776},
    {1016, 776},
    {1011, 776},
    {1011, 781},
    {1011, 786},
    {1011, 791},
    {1011, 796},
    {1006, 801},
    {1001, 801},
    {996, 801},
    {991, 801},
    {986, 801},
    {981, 801},
    {981, 806},
    {981, 811},
    {986, 815},
    {991, 820},
    {996, 820},
    {1001, 825},
    {1006, 830},
    {1011, 835},
    {1016, 840},
    {1021, 845},
    {1021, 850},
    {1016, 854},
    {1011, 859},
    {1006, 864},
    {1001, 869},
    {996, 864},
    {991, 859},
    {986, 859},
    {981, 859},
    {977, 859},
    {972, 859},
    {967, 859},
    {962, 859},
    {957, 859},
    {952, 859},
    {947, 859},
    {942, 859},
    {938, 859},
    {933, 859},
    {928, 859},
    {923, 859},
    {923, 864},
    {928, 869},
    {928, 874},
    {933, 874},
    {938, 874},
    {942, 874},
    {947, 874},
    {952, 874},
    {957, 874},
    {962, 874},
    {967, 874},
    {972, 874},
    {977, 874},
    {981, 874},
    {986, 874},
    {991, 874},
    {996, 869},
    {1001, 869},
    {1006, 874},
    {1001, 879},
    {1006, 879},
    {1011, 884},
    {1011, 889},
    {1011, 894},
    {1011, 898},
    {1011, 903},
    {1006, 908},
    {1001, 908},
    {996, 908},
    {991, 908},
    {986, 908},
    {981, 908},
    {977, 908},
    {972, 908},
    {967, 908},
    {962, 908},
    {957, 908},
    {952, 908},
    {947, 908},
    {942, 908},
    {938, 908},
    {933, 908},
    {928, 908},
    {928, 913},
    {923, 918},
    {928, 923},
    {928, 928},
    {933, 928},
    {938, 928},
    {942, 928},
    {947, 928},
    {952, 928},
    {957, 928},
    {962, 928},
    {967, 928},
    {972, 928},
    {977, 928},
    {981, 928},
    {986, 928},
    {991, 928},
    {996, 928},
    {1001, 928},
    {1006, 928},
    {1011, 928},
    {1016, 928},
    {1021, 928},
    {1025, 928},
    {1030, 928},
    {1035, 928},
    {1040, 933},
    {1040, 938},
    {1040, 942},
    {1040, 947},
    {1040, 952},
    {1040, 957},
    {1035, 962},
    {1030, 962},
    {1025, 962},
    {1021, 962},
    {1016, 962},
    {1011, 962},
    {1006, 962},
    {1001, 962},
    {996, 962},
    {991, 962},
    {986, 962},
    {981, 962},
    {977, 962},
    {972, 962},
    {967, 962},
    {962, 962},
    {957, 962},
    {952, 962},
    {947, 962},
    {942, 962},
    {938, 962},
    {933, 962},
    {928, 962},
    {923, 962},
    {918, 962},
    {913, 962},
    {908, 962},
    {903, 962},
    {898, 962},
    {894, 962},
    {889, 962},
    {884, 962},
    {879, 962},
    {874, 962},
    {869, 962},
    {864, 962},
    {859, 962},
    {854, 962},
    {850, 962},
    {845, 962},
    {840, 962},
    {835, 962},
    {830, 962},
    {825, 962},
    {820, 962},
    {815, 962},
    {811, 962},
    {806, 962},
    {801, 962},
    {796, 962},
    {791, 962},
    {786, 962},
    {781, 962},
    {776, 962},
    {771, 957},
    {771, 952},
    {771, 947},
    {771, 942},
    {771, 938},
    {776, 933},
    {781, 928},
    {786, 928},
    {791, 928},
    {796, 928},
    {801, 928},
    {806, 928},
    {811, 928},
    {815, 928},
    {820, 928},
    {825, 928},
    {830, 928},
    {835, 928},
    {840, 928},
    {845, 928},
    {850, 928},
    {854, 928},
    {859, 928},
    {864, 928},
    {869, 928},
    {874, 928},
    {879, 928},
    {884, 928},
    {889, 928},
    {889, 923},
    {889, 918},
    {889, 913},
    {884, 913},
    {879, 908},
    {874, 908},
    {869, 908},
    {864, 908},
    {859, 908},
    {854, 908},
    {850, 908},
    {845, 908},
    {840, 908},
    {835, 908},
    {830, 908},
    {825, 908},
    {820, 908},
    {815, 908},
    {811, 908},
    {806, 908},
    {801, 903},
    {801, 898},
    {801, 894},
    {801, 889},
    {801, 884},
    {801, 879},
    {806, 874},
    {811, 874},
    {815, 874},
    {820, 874},
    {825, 874},
    {830, 874},
    {835, 874},
    {840, 874},
    {845, 874},
    {850, 874},
    {854, 874},
    {859, 874},
    {864, 874},
    {869, 874},
    {874, 874},
    {879, 874},
    {884, 874},
    {889, 874},
    {889, 869},
    {889, 864},
    {889, 859},
    {884, 859},
    {879, 859},
    {874, 859},
    {869, 859},
    {864, 859},
    {859, 859},
    {854, 859},
    {850, 859},
    {845, 859},
    {840, 859},
    {835, 859},
    {830, 859},
    {825, 859},
    {820, 859},
    {815, 859},
    {811, 854},
    {806, 850},
    {801, 845},
    {801, 840},
    {801, 835},
    {801, 830},
    {806, 825},
    {811, 820},
    {815, 820},
    {820, 820},
    {825, 815},
    {830, 815},
    {835, 811},
    {840, 811},
    {845, 811},
    {840, 811},
    {835, 811},
    {830, 811},
    {825, 815},
    {820, 820},
    {815, 820},
    {811, 820},
    {806, 825},
    {801, 825},
    {801, 830},
    {801, 835},
    {801, 840},
    {801, 845},
    {806, 850},
    {806, 854},
    {811, 854},
    {815, 859},
    {820, 859},
    {825, 859},
    {830, 859},
    {835, 859},
    {840, 859},
    {845, 859},
    {850, 859},
    {854, 859},
    {859, 859},
    {864, 859},
    {869, 859},
    {874, 859},
    {879, 859},
    {884, 859},
    {889, 864},
    {889, 869},
    {884, 874},
    {879, 874},
    {874, 874},
    {869, 874},
    {864, 874},
    {859, 874},
    {854, 874},
    {850, 874},
    {845, 874},
    {840, 874},
    {835, 874},
    {830, 874},
    {825, 874},
    {820, 874},
    {815, 874},
    {811, 874},
    {806, 874},
    {801, 874},
    {801, 879},
    {801, 884},
    {801, 889},
    {801, 894},
    {801, 898},
    {801, 903},
    {801, 908},
    {806, 908},
    {811, 908},
    {815, 908},
    {820, 908},
    {825, 908},
    {830, 908},
    {835, 908},
    {840, 908},
    {845, 908},
    {850, 908},
    {854, 908},
    {859, 908},
    {864, 908},
    {869, 908},
    {874, 908},
    {879, 908},
    {884, 913},
    {889, 918},
    {889, 923},
    {884, 928},
    {879, 928},
    {874, 928},
    {869, 928},
    {864, 928},
    {859, 928},
    {854, 928},
    {850, 928},
    {845, 928},
    {840, 928},
    {835, 928},
    {830, 928},
    {825, 928},
    {820, 928},
    {815, 928},
    {811, 928},
    {806, 928},
    {801, 928},
    {796, 928},
    {791, 928},
    {786, 928},
    {781, 928},
    {776, 928},
    {776, 933},
    {771, 938},
    {771, 942},
    {771, 947},
    {771, 952},
    {771, 957},
    {776, 962},
    {781, 962},
    {786, 962},
    {791, 962},
    {796, 962},
    {801, 962},
    {806, 962},
    {811, 962},
    {815, 962},
    {820, 962},
    {825, 962},
    {830, 962},
    {835, 962},
    {840, 962},
    {845, 962},
    {850, 962},
    {854, 962},
    {859, 962},
    {864, 962},
    {869, 962},
    {874, 962},
    {879, 962},
    {884, 962},
    {889, 962},
    {894, 962},
    {898, 962},
    {903, 962},
    {908, 962},
    {913, 962},
    {918, 962},
    {923, 962},
    {928, 962},
    {933, 962},
    {938, 962},
    {942, 962},
    {947, 962},
    {952, 962},
    {957, 962},
    {962, 962},
    {967, 962},
    {972, 962},
    {977, 962},
    {981, 962},
    {986, 962},
    {991, 962},
    {996, 962},
    {1001, 962},
    {1006, 962},
    {1011, 962},
    {1016, 962},
    {1021, 962},
    {1025, 962},
    {1030, 962},
    {1035, 962},
    {1040, 962},
    {1040, 957},
    {1040, 952},
    {1040, 947},
    {1040, 942},
    {1040, 938},
    {1040, 933},
    {1040, 928},
    {1035, 928},
    {1030, 928},
    {1025, 928},
    {1021, 928},
    {1016, 928},
    {1011, 928},
    {1006, 928},
    {1001, 928},
    {996, 928},
    {991, 928},
    {986, 928},
    {981, 928},
    {977, 928},
    {972, 928},
    {967, 928},
    {962, 928},
    {957, 928},
    {952, 928},
    {947, 928},
    {942, 928},
    {938, 928},
    {933, 928},
    {928, 923},
    {923, 918},
    {928, 913},
    {933, 908},
    {938, 908},
    {942, 908},
    {947, 908},
    {952, 908},
    {957, 908},
    {962, 908},
    {967, 908},
    {972, 908},
    {977, 908},
    {981, 908},
    {986, 908},
    {991, 908},
    {996, 908},
    {1001, 908},
    {1006, 908},
    {1011, 908},
    {1011, 903},
    {1011, 898},
    {1011, 894},
    {1011, 889},
    {1011, 884},
    {1011, 879},
    {1006, 874},
    {1006, 869},
    {1011, 864},
    {1016, 859},
    {1021, 854},
    {1025, 850},
    {1021, 845},
    {1021, 840},
    {1016, 835},
    {1011, 830},
    {1006, 825},
    {1001, 820},
    {996, 820},
    {991, 815},
    {986, 811},
    {981, 806},
    {986, 801},
    {991, 801},
    {996, 801},
    {1001, 801},
    {1006, 801},
    {1011, 801},
    {1011, 796},
    {1011, 791},
    {1011, 786},
    {1011, 781},
    {1016, 776},
    {1021, 776},
    {1025, 776},
    {1030, 776},
    {1030, 771},
    {1035, 767},
    {1035, 762},
    {1035, 757},
    {1035, 752},
    {1035, 747},
    {1035, 742},
    {1035, 737},
    {1035, 732},
    {1035, 728},
    {1030, 723},
    {1030, 718},
    {1025, 718},
    {1021, 718},
    {1016, 718},
    {1011, 718},
    {1006, 718},
    {1001, 718},
    {996, 718},
    {991, 718},
    {986, 718},
    {981, 718},
    {977, 718},
    {972, 718},
    {967, 718},
    {962, 718},
    {957, 718},
    {952, 718},
    {947, 718},
    {942, 718},
    {938, 718},
    {933, 713},
    {928, 708},
    {928, 703},
    {923, 698},
    {923, 693},
    {918, 693},
    {913, 693},
    {908, 693},
    {903, 693},
    {898, 693},
    {894, 693},
    {889, 693},
    {903, 801},
    {898, 806},
    {894, 806},
    {889, 811},
    {884, 815},
    {879, 815},
    {884, 815},
    {889, 815},
    {894, 811},
    {898, 806},
    {903, 801},
    {908, 801},
    {913, 801},
    {918, 801},
    {923, 801},
    {928, 801},
    {933, 801},
    {938, 801},
    {942, 801},
    {938, 801},
    {933, 801},
    {928, 801},
    {923, 801},
    {918, 801},
    {913, 801},
    {908, 801},
    {938, 811},
    {938, 815},
    {942, 820},
    {938, 825},
    {933, 825},
    {928, 825},
    {923, 825},
    {918, 825},
    {913, 825},
    {908, 825},
    {903, 825},
    {898, 825},
    {894, 825},
    {889, 825},
    {884, 825},
    {879, 825},
    {884, 825},
    {889, 825},
    {894, 825},
    {898, 825},
    {903, 825},
    {908, 825},
    {913, 825},
    {918, 825},
    {923, 825},
    {928, 825},
    {933, 825},
    {938, 825},
    {942, 825},
    {942, 820},
    {938, 815},
    {942, 811},
    {923, 864},
    {928, 859},
    {933, 859},
    {938, 859},
    {942, 859},
    {947, 859},
    {952, 859},
    {957, 859},
    {962, 859},
    {967, 859},
    {972, 859},
    {977, 859},
    {981, 859},
    {986, 859},
    {991, 864},
    {996, 869},
    {991, 874},
    {986, 874},
    {981, 874},
    {977, 874},
    {972, 874},
    {967, 874},
    {962, 874},
    {957, 874},
    {952, 874},
    {947, 874},
    {942, 874},
    {938, 874},
    {933, 874},
    {928, 869}};

/**
 * @brief 轨迹运动任务函数
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void trajectory_task(MultiTimer *timer, void *userData)
{
    if (!trajectory_running || current_segment_index >= NUM_DRAWING_POINTS)
    {
        trajectory_running = false; // 运动完成，停止
        return;
    }

    // 获取当前点的坐标(忽略第三维)
    int32_t x_pulse = drawing_points[current_segment_index][0];
    int32_t y_pulse = drawing_points[current_segment_index][1];

    // 控制云台运动到指定位置
    Motor_Set_Position(x_pulse, y_pulse);

    // 移动到下一个点
    current_segment_index++;

    // 继续下一步运动
    if (current_segment_index < NUM_DRAWING_POINTS)
    {
        multiTimerStart(&mt_trajectory, LASER_DRAW_STEP_DELAY, trajectory_task, NULL);
    }
    else
    {
        trajectory_running = false; // 运动完成
    }
}

/**
 * @brief 启动轨迹运动
 */
void start_trajectory_movement(void)
{
    if (trajectory_running)
    {
        return; // 如果正在运动，直接返回
    }

    current_segment_index = 0; // 重置索引
    trajectory_running = true; // 设置运动状态

    // 启动轨迹运动定时器
    multiTimerStart(&mt_trajectory, LASER_DRAW_STEP_DELAY, trajectory_task, NULL);
}

/**
 * @brief 停止轨迹运动
 */
void stop_trajectory_movement(void)
{
    trajectory_running = false;
}

/**
 * @brief 获取轨迹运动状态
 * @return true:正在运动 false:已停止
 */
bool get_trajectory_status(void)
{
    return trajectory_running;
}

/**
 * @brief 获取当前运动进度
 * @return 当前点索引
 */
uint32_t get_trajectory_progress(void)
{
    return current_segment_index;
}
