/**
 * @file app_trajectory.h
 * @brief 轨迹控制模块
 * @copyright 米醋电子工作室
 */

#ifndef __APP_TRAJECTORY_H_
#define __APP_TRAJECTORY_H_

#include "mydefine.h"

/* 轨迹控制配置 */
#define TRAJECTORY_STEP_SIZE 5        // 插补步长(像素)
#define TRAJECTORY_ARRIVE_THRESHOLD 3 // 到达判断阈值(像素)
#define TRAJECTORY_UPDATE_PERIOD 10   // 轨迹更新周期(ms) - 减小周期提高精度
#define MAX_POLYLINE_POINTS 50        // 多点轨迹最大点数
#define TRAJECTORY_SMOOTH_FACTOR 0.8f // 轨迹平滑系数(0-1)

/* 轨迹点结构体 */
typedef struct
{
    int x, y;
} trajectory_point_t;

/* 轨迹结构体（通用） */
typedef struct
{
    trajectory_point_t corners[4];     // 四个角点
    uint8_t current_edge;              // 当前边 (0-3)
    uint16_t current_step;             // 当前步数
    uint16_t total_steps_per_edge;     // 每条边的总步数
    uint8_t trajectory_running;        // 运动状态标志
    uint8_t move_to_start;             // 是否正在移动到起始点
    uint8_t is_resetting;              // 是否正在复位到中心
    uint8_t is_returning;              // 是否正在返回原位置
    trajectory_point_t saved_position; // 保存的复位前位置
    uint8_t saved_edge;                // 保存的边索引
    uint16_t saved_step;               // 保存的步数
} trajectory_rect_t;

/* 多点轨迹结构体 */
typedef struct
{
    trajectory_point_t points[MAX_POLYLINE_POINTS]; // 轨迹点数组
    uint8_t point_count;                            // 实际点数
    uint8_t current_segment;                        // 当前线段索引
    uint16_t current_step;                          // 当前步数
    uint16_t total_steps_per_segment;               // 每段的总步数
    uint8_t trajectory_running;                     // 运动状态标志
    uint8_t move_to_start;                          // 是否正在移动到起始点
    uint8_t is_resetting;                           // 是否正在复位到中心
    uint8_t is_returning;                           // 是否正在返回原位置
    trajectory_point_t saved_position;              // 保存的复位前位置
    uint8_t saved_segment;                          // 保存的线段索引
    uint16_t saved_step;                            // 保存的步数
    trajectory_point_t last_target;                 // 上一个目标点（用于平滑）
    trajectory_point_t smooth_target;               // 平滑后的目标点
} trajectory_polyline_t;

/* 全局变量声明 */
extern trajectory_rect_t pencil_trajectory;       // 铅笔框轨迹
extern trajectory_rect_t vector_trajectory;       // 矩形框轨迹
extern trajectory_polyline_t polyline_trajectory; // 多点轨迹
extern MultiTimer mt_pencil_trajectory;           // 铅笔框定时器
extern MultiTimer mt_vector_trajectory;           // 矩形框定时器
extern MultiTimer mt_polyline_trajectory;         // 多点轨迹定时器

/* 外部位置变量声明（来自PID模块） */
extern int current_x, current_y; // 摄像头识别的当前位置

/* 函数声明 */
void app_trajectory_init(void); // 轨迹初始化

// 铅笔框循迹（简单线性插值）
void app_pencil_set_rect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4); // 设置铅笔框参数
void app_pencil_start(void);                                                              // 开始铅笔框运动
void app_pencil_stop(void);                                                               // 停止铅笔框运动
void app_pencil_restart(void);                                                            // 重新开始铅笔框运动
void app_pencil_reset_to_center(void);                                                    // 复位到铅笔框中心
void app_pencil_return_and_continue(void);                                                // 返回原位置并继续循迹
void app_pencil_task(MultiTimer *timer, void *userData);                                  // 铅笔框任务函数
trajectory_point_t app_pencil_calc_next_point(void);                                      // 计算铅笔框轨迹点
trajectory_point_t calc_pencil_center(void);                                              // 计算铅笔框中心点

// 矩形框循迹（向量参数化插值）
void app_vector_set_rect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4); // 设置矩形框参数
void app_vector_start(void);                                                              // 开始矩形框运动
void app_vector_stop(void);                                                               // 停止矩形框运动
void app_vector_restart(void);                                                            // 重新开始矩形框运动
void app_vector_reset_to_center(void);                                                    // 复位到矩形框中心
void app_vector_return_and_continue(void);                                                // 返回原位置并继续循迹
void app_vector_task(MultiTimer *timer, void *userData);                                  // 矩形框任务函数

// 多点轨迹循迹（样条插值）
void app_polyline_set_points(int *coords, int point_count);                   // 设置多点轨迹参数
void app_polyline_start(void);                                                // 开始多点轨迹运动
void app_polyline_stop(void);                                                 // 停止多点轨迹运动
void app_polyline_restart(void);                                              // 重新开始多点轨迹运动
void app_polyline_reset_to_center(void);                                      // 复位到多点轨迹中心
void app_polyline_return_and_continue(void);                                  // 返回原位置并继续循迹
void app_polyline_task(MultiTimer *timer, void *userData);                    // 多点轨迹任务函数
trajectory_point_t app_polyline_calc_next_point(void);                        // 计算多点轨迹点
trajectory_point_t app_polyline_smooth_target(trajectory_point_t raw_target); // 轨迹平滑处理
trajectory_point_t calc_polyline_center(void);                                // 计算多点轨迹中心点

// 矩形框循迹辅助函数
trajectory_point_t app_vector_calc_next_point(void);                                         // 计算矩形框轨迹点
trajectory_point_t calc_vector_center(void);                                                 // 计算矩形框中心点
uint8_t app_trajectory_check_arrival(trajectory_point_t target, trajectory_point_t current); // 检查是否到达目标点

#endif /* __APP_TRAJECTORY_H_ */
