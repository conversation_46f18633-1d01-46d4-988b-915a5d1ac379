# 激光绘图系统

基于STM32F4的激光绘图控制系统，支持多种绘图模式和轨迹控制。

## 系统特性

- 双轴电机精确控制
- 多种绘图函数支持（直角坐标、参数方程、极坐标）
- PID闭环控制
- 实时轨迹规划
- 串口通信控制
- OLED显示界面

## 硬件配置

### 串口配置 (2024年更新)
- USART1: 调试串口 (115200)
- USART2: X轴电机通信 (115200) - 原UART4功能移植
- USART3: 摄像头通信 (115200) - 原USART2功能移植  
- UART4: Y轴电机通信 (115200) - 原UART5功能移植
- ~~UART5: 已删除~~
- ~~HMI人机界面: 已删除~~

### 电机配置
- X轴电机: 连接USART2，地址0x01 (原UART4)
- Y轴电机: 连接UART4，地址0x01 (原UART5)
- 最大转速: 20 RPM
- 角度限制: ±50度

### GPIO配置
- 激光控制: GPIOB Pin 0
- 按键输入: 支持多按键检测
- I2C1: OLED显示屏
- I2C2: 扩展接口

## 软件架构

### 核心模块
- `app_motor.c/h`: 电机控制模块
- `app_laser_draw.c/h`: 激光绘图模块
- `app_trajectory.c/h`: 轨迹规划模块
- `app_pid.c/h`: PID控制模块
- `app_maixcam.c/h`: 摄像头通信模块
- ~~`app_hmi.c/h`: 人机界面模块 (已删除)~~

### 通信协议
- 电机通信: EMM V5协议
- ~~HMI通信: 已删除~~
- 摄像头通信: 自定义数据格式

### 绘图功能
支持多种数学函数绘制：
- 螺旋线 (极坐标)
- 正弦波 (直角坐标)
- 玫瑰线 (极坐标)
- 菱形 (参数方程)

## 使用说明

### 串口命令
- `reset`: 电机回到初始位置
- `set(x,y)`: 设置PID目标位置
- `mode1/mode2/mode3`: 切换摄像头模式

### ~~HMI控制 (已删除)~~

## 编译说明

使用Keil MDK-ARM 5.x编译：
1. 打开 `MDK-ARM/project.uvprojx`
2. 选择目标配置
3. 编译并下载到STM32F4开发板

## 更新日志

### 2024年更新 - UART重新配置
- **UART4内容移植到USART2**: X轴电机通信从UART4迁移到USART2
- **UART5内容移植到UART4**: Y轴电机通信从UART5迁移到UART4  
- **USART2内容移植到USART3**: 摄像头通信从USART2迁移到USART3
- **删除HMI功能**: 完全移除HMI人机界面相关代码和USART3原有功能
- **删除UART5**: 不再使用UART5外设
- **更新电机配置**: 修改app_motor.h中的UART宏定义
- **清理相关变量**: 删除所有HMI相关的缓冲区和任务定义

## 注意事项

- 确保电机连接正确，避免超出角度限制
- 激光使用时注意安全防护
- 串口波特率统一设置为115200
- 系统时钟配置为168MHz
- **重要**: 硬件连接需要根据新的串口配置进行调整
