/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LED2_Pin GPIO_PIN_2
#define LED2_GPIO_Port GPIOE
#define LED3_Pin GPIO_PIN_3
#define LED3_GPIO_Port GPIOE
#define LED4_Pin GPIO_PIN_4
#define LED4_GPIO_Port GPIOE
#define LED5_Pin GPIO_PIN_5
#define LED5_GPIO_Port GPIOE
#define LED6_Pin GPIO_PIN_6
#define LED6_GPIO_Port GPIOE
#define COL_0_Pin GPIO_PIN_3
#define COL_0_GPIO_Port GPIOD
#define COL_1_Pin GPIO_PIN_4
#define COL_1_GPIO_Port GPIOD
#define COL_2_Pin GPIO_PIN_5
#define COL_2_GPIO_Port GPIOD
#define COL_3_Pin GPIO_PIN_6
#define COL_3_GPIO_Port GPIOD
#define BEEP_Pin GPIO_PIN_7
#define BEEP_GPIO_Port GPIOD
#define ROW_0_Pin GPIO_PIN_3
#define ROW_0_GPIO_Port GPIOB
#define ROW_1_Pin GPIO_PIN_4
#define ROW_1_GPIO_Port GPIOB
#define ROW_2_Pin GPIO_PIN_5
#define ROW_2_GPIO_Port GPIOB
#define ROW_3_Pin GPIO_PIN_6
#define ROW_3_GPIO_Port GPIOB
#define LED0_Pin GPIO_PIN_0
#define LED0_GPIO_Port GPIOE
#define LED1_Pin GPIO_PIN_1
#define LED1_GPIO_Port GPIOE

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
