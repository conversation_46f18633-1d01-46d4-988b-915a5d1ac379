#ifndef __MYDEFINE_
#define __MYDEFINE_

#include "stdlib.h"
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "main.h"

#include "Emm_V5.h"
#include "ringbuffer.h"
#include "MultiTimer.h"

#include "app_oled.h"
#include "app_motor.h"
#include "app_uasrt.h"
#include "app_maixcam.h"
#include "app_pid.h"
#include "app_trajectory.h"
#include "app_botton.h"
#include "app_hmi.h"
#include "app_laser_draw.h"

extern UART_HandleTypeDef huart5;
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern UART_HandleTypeDef huart3;
extern UART_HandleTypeDef huart4;
extern MultiTimer mt_system, mt_oled, mt_usart, mt_cam, mt_pid, mt_user, mt_botton, mt_hmi, mt_polyline_trajectory;

extern uint8_t motor_x_buf[128];
extern uint8_t motor_y_buf[128];
extern uint8_t cam_rx_buf[128];
extern uint8_t user_rx_buf[128];
extern uint8_t hmi_rx_buf[128];
extern uint8_t ringbuffer_pool_x[128];
extern uint8_t ringbuffer_pool_y[128];
extern uint8_t ringbuffer_pool_cam[128];
extern uint8_t ringbuffer_pool_user[128];
extern uint8_t ringbuffer_pool_hmi[128];
extern uint8_t output_buffer_x[128];
extern uint8_t output_buffer_y[128];
extern uint8_t output_buffer_cam[128];
extern uint8_t output_buffer_user[128];
extern uint8_t output_buffer_hmi[128];
extern struct rt_ringbuffer ringbuffer_x;
extern struct rt_ringbuffer ringbuffer_y;
extern struct rt_ringbuffer ringbuffer_cam;
extern struct rt_ringbuffer ringbuffer_user;
extern struct rt_ringbuffer ringbuffer_hmi;

extern int text01[10];
extern uint8_t uart_flag;

#define OLED_TASK_TIME 100
#define BOTTON_TASK_TIME 100
#define USART_TASK_TIME 10

#endif /*__MYDEFINE_*/
