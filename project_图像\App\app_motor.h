/**
 * @file app_motor.h
 * @brief 基于EmmV5的电机控制函数
 * @copyright 白蛋电子工作室
 */

#ifndef __APP_MOTOR_H_
#define __APP_MOTOR_H_

#include "mydefine.h"

/* 电机控制宏定义 */
#define MOTOR_X_ADDR 0x01     // X轴电机地址
#define MOTOR_Y_ADDR 0x01     // Y轴电机地址
#define MOTOR_X_UART huart2   // X轴电机串口句柄 (UART4内容移植到USART2)
#define MOTOR_Y_UART huart4   // Y轴电机串口句柄 (UART5内容移植到UART4)
#define MOTOR_MAX_SPEED 20    // 最大转速(RPM)
#define MOTOR_PUL_SPEED 1500    // 最大转速(RPM)
#define MOTOR_ACCEL 0         // 加速度(0表示直接启动)
#define MOTOR_SYNC_FLAG false // 同步标志
#define MOTOR_MAX_ANGLE 50    // 最大角度限制(±50度)

/* 函数声明 */
void Motor_Init(void);                                       // 电机初始化
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);    // 设置XY轴速度(百分比)
void Motor_Set_Position(int32_t x_pulses, int32_t y_pulses); // 设置XY轴位置(脉冲)
void Motor_Stop(void);                                       // 停止所有电机

#endif /* __APP_MOTOR_H_ */
